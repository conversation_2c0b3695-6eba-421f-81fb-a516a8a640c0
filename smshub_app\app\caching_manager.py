"""
Caching manager for the SMSHub application.

This module provides Redis-based caching for improved performance,
including SMS message caching, modem status caching, and configuration caching.
"""

import json
import logging
import time
import threading
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

try:
    from .config_enhanced import get_enhanced_config
    from .logging_enhanced import get_logger, operation_timer
    from .error_handling import with_error_handling, ErrorCategory, ErrorSeverity, report_error
except ImportError:
    from config_enhanced import get_enhanced_config
    from logging_enhanced import get_logger, operation_timer
    from error_handling import with_error_handling, ErrorCategory, ErrorSeverity, report_error

logger = get_logger(__name__, "CachingManager")


@dataclass
class CacheConfig:
    """Configuration for caching system."""
    enabled: bool = True
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_db: int = 0
    redis_password: Optional[str] = None
    default_ttl: int = 300  # 5 minutes
    sms_cache_ttl: int = 600  # 10 minutes
    modem_status_ttl: int = 30  # 30 seconds
    config_cache_ttl: int = 3600  # 1 hour
    max_retries: int = 3
    retry_delay: float = 1.0


class InMemoryCache:
    """
    Fallback in-memory cache when Redis is not available.
    
    Provides basic caching functionality with TTL support
    using Python dictionaries.
    """

    def __init__(self, default_ttl: int = 300):
        """Initialize in-memory cache."""
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.RLock()

    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        with self._lock:
            if key not in self._cache:
                return None
            
            entry = self._cache[key]
            if time.time() > entry["expires_at"]:
                del self._cache[key]
                return None
            
            return entry["value"]

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            with self._lock:
                expires_at = time.time() + (ttl or self.default_ttl)
                self._cache[key] = {
                    "value": value,
                    "expires_at": expires_at,
                    "created_at": time.time()
                }
                return True
        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False

    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False

    def clear(self) -> bool:
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            return True

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            current_time = time.time()
            expired_count = 0
            
            for entry in self._cache.values():
                if current_time > entry["expires_at"]:
                    expired_count += 1
            
            return {
                "type": "in_memory",
                "total_keys": len(self._cache),
                "expired_keys": expired_count,
                "active_keys": len(self._cache) - expired_count
            }


class RedisCache:
    """
    Redis-based cache implementation.
    
    Provides high-performance caching with Redis backend
    including connection pooling and error handling.
    """

    def __init__(self, config: CacheConfig):
        """Initialize Redis cache."""
        self.config = config
        self._redis: Optional[redis.Redis] = None
        self._connection_pool: Optional[redis.ConnectionPool] = None
        self._connected = False

    def _connect(self) -> bool:
        """Establish Redis connection."""
        try:
            if self._connected and self._redis:
                return True

            # Create connection pool
            self._connection_pool = redis.ConnectionPool(
                host=self.config.redis_host,
                port=self.config.redis_port,
                db=self.config.redis_db,
                password=self.config.redis_password,
                decode_responses=True,
                max_connections=20,
                retry_on_timeout=True
            )

            # Create Redis client
            self._redis = redis.Redis(connection_pool=self._connection_pool)
            
            # Test connection
            self._redis.ping()
            self._connected = True
            
            logger.info(f"Connected to Redis at {self.config.redis_host}:{self.config.redis_port}")
            return True

        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self._connected = False
            return False

    def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache."""
        try:
            if not self._connect():
                return None

            value = self._redis.get(key)
            if value is None:
                return None

            # Try to deserialize JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return value

        except Exception as e:
            logger.error(f"Error getting cache key {key}: {e}")
            return None

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in Redis cache."""
        try:
            if not self._connect():
                return False

            # Serialize value to JSON if it's not a string
            if not isinstance(value, str):
                try:
                    value = json.dumps(value, default=str)
                except (TypeError, ValueError) as e:
                    logger.error(f"Error serializing value for key {key}: {e}")
                    return False

            # Set with TTL
            ttl = ttl or self.config.default_ttl
            result = self._redis.setex(key, ttl, value)
            return bool(result)

        except Exception as e:
            logger.error(f"Error setting cache key {key}: {e}")
            return False

    def delete(self, key: str) -> bool:
        """Delete key from Redis cache."""
        try:
            if not self._connect():
                return False

            result = self._redis.delete(key)
            return bool(result)

        except Exception as e:
            logger.error(f"Error deleting cache key {key}: {e}")
            return False

    def clear(self) -> bool:
        """Clear all cache entries."""
        try:
            if not self._connect():
                return False

            self._redis.flushdb()
            return True

        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False

    def get_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics."""
        try:
            if not self._connect():
                return {"type": "redis", "connected": False}

            info = self._redis.info()
            return {
                "type": "redis",
                "connected": True,
                "used_memory": info.get("used_memory_human", "N/A"),
                "total_keys": info.get("db0", {}).get("keys", 0) if "db0" in info else 0,
                "expired_keys": info.get("expired_keys", 0),
                "evicted_keys": info.get("evicted_keys", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0)
            }

        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"type": "redis", "connected": False, "error": str(e)}


class CachingManager:
    """
    Main caching manager that provides unified caching interface.
    
    Automatically falls back to in-memory cache if Redis is unavailable.
    """

    def __init__(self, config: Optional[CacheConfig] = None):
        """Initialize caching manager."""
        self.config = config or CacheConfig()
        self._cache: Union[RedisCache, InMemoryCache, None] = None
        self._initialized = False

    def _initialize(self) -> bool:
        """Initialize the cache backend."""
        if self._initialized:
            return True

        if not self.config.enabled:
            logger.info("Caching is disabled")
            return False

        # Try Redis first if available
        if REDIS_AVAILABLE:
            redis_cache = RedisCache(self.config)
            if redis_cache._connect():
                self._cache = redis_cache
                logger.info("Using Redis cache backend")
            else:
                logger.warning("Redis connection failed, falling back to in-memory cache")
                self._cache = InMemoryCache(self.config.default_ttl)
        else:
            logger.warning("Redis not available, using in-memory cache")
            self._cache = InMemoryCache(self.config.default_ttl)

        self._initialized = True
        return True

    @with_error_handling("CachingManager", ErrorCategory.UNKNOWN, ErrorSeverity.LOW)
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self._initialize():
            return None

        with operation_timer(logger, f"cache_get_{key}"):
            return self._cache.get(key)

    @with_error_handling("CachingManager", ErrorCategory.UNKNOWN, ErrorSeverity.LOW)
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        if not self._initialize():
            return False

        with operation_timer(logger, f"cache_set_{key}"):
            return self._cache.set(key, value, ttl)

    @with_error_handling("CachingManager", ErrorCategory.UNKNOWN, ErrorSeverity.LOW)
    def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if not self._initialize():
            return False

        return self._cache.delete(key)

    def clear(self) -> bool:
        """Clear all cache entries."""
        if not self._initialize():
            return False

        return self._cache.clear()

    # Specialized caching methods
    def cache_sms_messages(self, messages: List[Dict[str, Any]], key_suffix: str = "") -> bool:
        """Cache SMS messages with appropriate TTL."""
        key = f"sms_messages:{key_suffix}" if key_suffix else "sms_messages:all"
        return self.set(key, messages, self.config.sms_cache_ttl)

    def get_cached_sms_messages(self, key_suffix: str = "") -> Optional[List[Dict[str, Any]]]:
        """Get cached SMS messages."""
        key = f"sms_messages:{key_suffix}" if key_suffix else "sms_messages:all"
        return self.get(key)

    def cache_modem_status(self, port: str, status: Dict[str, Any]) -> bool:
        """Cache modem status with short TTL."""
        key = f"modem_status:{port}"
        return self.set(key, status, self.config.modem_status_ttl)

    def get_cached_modem_status(self, port: str) -> Optional[Dict[str, Any]]:
        """Get cached modem status."""
        key = f"modem_status:{port}"
        return self.get(key)

    def cache_configuration(self, config_data: Dict[str, Any]) -> bool:
        """Cache configuration data."""
        return self.set("app_config", config_data, self.config.config_cache_ttl)

    def get_cached_configuration(self) -> Optional[Dict[str, Any]]:
        """Get cached configuration."""
        return self.get("app_config")

    def invalidate_modem_cache(self, port: str) -> bool:
        """Invalidate cache for a specific modem."""
        return self.delete(f"modem_status:{port}")

    def invalidate_sms_cache(self) -> bool:
        """Invalidate SMS message cache."""
        # Delete all SMS-related keys
        keys_to_delete = ["sms_messages:all", "sms_messages:recent", "sms_messages:pending"]
        success = True
        for key in keys_to_delete:
            if not self.delete(key):
                success = False
        return success

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        if not self._initialize():
            return {"enabled": False}

        stats = self._cache.get_stats()
        stats.update({
            "enabled": self.config.enabled,
            "config": asdict(self.config)
        })
        return stats


# Global caching manager instance
_caching_manager: Optional[CachingManager] = None


def get_caching_manager() -> CachingManager:
    """Get the global caching manager instance."""
    global _caching_manager
    if _caching_manager is None:
        # Load cache config from app config
        app_config = get_enhanced_config()
        cache_config = CacheConfig(
            enabled=getattr(app_config, 'cache_enabled', True),
            redis_host=getattr(app_config, 'redis_host', 'localhost'),
            redis_port=getattr(app_config, 'redis_port', 6379),
            redis_db=getattr(app_config, 'redis_db', 0),
            redis_password=getattr(app_config, 'redis_password', None)
        )
        _caching_manager = CachingManager(cache_config)
    return _caching_manager


# Convenience functions
def cache_get(key: str) -> Optional[Any]:
    """Get value from cache (convenience function)."""
    return get_caching_manager().get(key)


def cache_set(key: str, value: Any, ttl: Optional[int] = None) -> bool:
    """Set value in cache (convenience function)."""
    return get_caching_manager().set(key, value, ttl)


def cache_delete(key: str) -> bool:
    """Delete key from cache (convenience function)."""
    return get_caching_manager().delete(key)


def cache_clear() -> bool:
    """Clear all cache entries (convenience function)."""
    return get_caching_manager().clear()
