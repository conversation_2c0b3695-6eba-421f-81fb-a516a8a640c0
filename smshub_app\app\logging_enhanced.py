"""
Enhanced logging module for the SMSHub application.

This module provides structured logging with request ID tracking,
log rotation, and different configurations for development and production.
"""

import logging
import logging.handlers
import json
import time
import threading
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from contextlib import contextmanager

try:
    from .config_enhanced import get_enhanced_config
except ImportError:
    from config_enhanced import get_enhanced_config


@dataclass
class LogEntry:
    """Structured log entry."""
    timestamp: str
    level: str
    logger_name: str
    message: str
    request_id: Optional[str] = None
    component: Optional[str] = None
    operation: Optional[str] = None
    duration_ms: Optional[float] = None
    error_type: Optional[str] = None
    stack_trace: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values."""
        return {k: v for k, v in asdict(self).items() if v is not None}


class RequestIDFilter(logging.Filter):
    """Filter to add request ID to log records."""
    
    def filter(self, record):
        """Add request ID to the log record."""
        record.request_id = getattr(_request_context, 'request_id', None)
        record.component = getattr(_request_context, 'component', None)
        record.operation = getattr(_request_context, 'operation', None)
        return True


class StructuredFormatter(logging.Formatter):
    """Formatter for structured JSON logging."""
    
    def format(self, record):
        """Format log record as structured JSON."""
        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created).isoformat(),
            level=record.levelname,
            logger_name=record.name,
            message=record.getMessage(),
            request_id=getattr(record, 'request_id', None),
            component=getattr(record, 'component', None),
            operation=getattr(record, 'operation', None)
        )
        
        # Add exception information if present
        if record.exc_info:
            log_entry.error_type = record.exc_info[0].__name__ if record.exc_info[0] else None
            log_entry.stack_trace = self.formatException(record.exc_info)
        
        # Add any extra fields
        extra_fields = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 'msecs',
                          'relativeCreated', 'thread', 'threadName', 'processName', 'process',
                          'getMessage', 'exc_info', 'exc_text', 'stack_info', 'request_id',
                          'component', 'operation']:
                extra_fields[key] = value
        
        if extra_fields:
            log_entry.metadata = extra_fields
        
        return json.dumps(log_entry.to_dict(), default=str)


class HumanReadableFormatter(logging.Formatter):
    """Formatter for human-readable console output."""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s - %(name)s - %(levelname)s - [%(request_id)s] [%(component)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    def format(self, record):
        """Format log record for human reading."""
        # Ensure request_id and component are present
        if not hasattr(record, 'request_id'):
            record.request_id = 'N/A'
        if not hasattr(record, 'component'):
            record.component = 'N/A'
        
        return super().format(record)


class RequestContext:
    """Thread-local context for request tracking."""
    
    def __init__(self):
        self._local = threading.local()
    
    @property
    def request_id(self) -> Optional[str]:
        """Get current request ID."""
        return getattr(self._local, 'request_id', None)
    
    @request_id.setter
    def request_id(self, value: str):
        """Set current request ID."""
        self._local.request_id = value
    
    @property
    def component(self) -> Optional[str]:
        """Get current component."""
        return getattr(self._local, 'component', None)
    
    @component.setter
    def component(self, value: str):
        """Set current component."""
        self._local.component = value
    
    @property
    def operation(self) -> Optional[str]:
        """Get current operation."""
        return getattr(self._local, 'operation', None)
    
    @operation.setter
    def operation(self, value: str):
        """Set current operation."""
        self._local.operation = value
    
    def clear(self):
        """Clear all context."""
        for attr in ['request_id', 'component', 'operation']:
            if hasattr(self._local, attr):
                delattr(self._local, attr)


class LoggingManager:
    """Enhanced logging manager with structured logging and rotation."""
    
    def __init__(self):
        self.config = get_enhanced_config()
        self._configured = False
        self._log_dir = Path(__file__).parent.parent / "logs"
        self._handlers: Dict[str, logging.Handler] = {}
    
    def configure_logging(self, environment: str = "development") -> None:
        """
        Configure logging based on environment.
        
        Args:
            environment: Environment type (development, production, testing)
        """
        if self._configured:
            return
        
        # Create logs directory
        self._log_dir.mkdir(exist_ok=True)
        
        # Get root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.logging.level.upper()))
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Configure based on environment
        if environment == "production":
            self._configure_production_logging()
        elif environment == "testing":
            self._configure_testing_logging()
        else:
            self._configure_development_logging()
        
        # Add request ID filter to all handlers
        request_filter = RequestIDFilter()
        for handler in root_logger.handlers:
            handler.addFilter(request_filter)
        
        self._configured = True
        logging.info(f"Logging configured for {environment} environment")
    
    def _configure_development_logging(self) -> None:
        """Configure logging for development environment."""
        root_logger = logging.getLogger()
        
        # Console handler with human-readable format
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(HumanReadableFormatter())
        root_logger.addHandler(console_handler)
        self._handlers['console'] = console_handler
        
        # File handler for all logs
        file_handler = logging.handlers.RotatingFileHandler(
            self._log_dir / "smshub_dev.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(file_handler)
        self._handlers['file'] = file_handler
    
    def _configure_production_logging(self) -> None:
        """Configure logging for production environment."""
        root_logger = logging.getLogger()
        
        # Console handler with structured format
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(console_handler)
        self._handlers['console'] = console_handler
        
        # Application log file
        app_handler = logging.handlers.RotatingFileHandler(
            self._log_dir / "smshub_app.log",
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10
        )
        app_handler.setLevel(logging.INFO)
        app_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(app_handler)
        self._handlers['app'] = app_handler
        
        # Error log file
        error_handler = logging.handlers.RotatingFileHandler(
            self._log_dir / "smshub_error.log",
            maxBytes=20*1024*1024,  # 20MB
            backupCount=10
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(StructuredFormatter())
        root_logger.addHandler(error_handler)
        self._handlers['error'] = error_handler
        
        # SMS operations log
        sms_logger = logging.getLogger('sms_operations')
        sms_handler = logging.handlers.RotatingFileHandler(
            self._log_dir / "sms_operations.log",
            maxBytes=100*1024*1024,  # 100MB
            backupCount=20
        )
        sms_handler.setLevel(logging.INFO)
        sms_handler.setFormatter(StructuredFormatter())
        sms_logger.addHandler(sms_handler)
        self._handlers['sms'] = sms_handler
    
    def _configure_testing_logging(self) -> None:
        """Configure logging for testing environment."""
        root_logger = logging.getLogger()
        
        # Only console handler for testing
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # Reduce noise in tests
        console_handler.setFormatter(HumanReadableFormatter())
        root_logger.addHandler(console_handler)
        self._handlers['console'] = console_handler
    
    def get_logger(self, name: str, component: str = None) -> logging.Logger:
        """
        Get a logger with optional component context.
        
        Args:
            name: Logger name
            component: Component name for context
            
        Returns:
            Configured logger
        """
        logger = logging.getLogger(name)
        
        if component:
            # Create a wrapper that automatically sets component context
            class ComponentLogger:
                def __init__(self, logger, component):
                    self._logger = logger
                    self._component = component
                
                def _log_with_component(self, level, msg, *args, **kwargs):
                    old_component = _request_context.component
                    _request_context.component = self._component
                    try:
                        getattr(self._logger, level)(msg, *args, **kwargs)
                    finally:
                        _request_context.component = old_component
                
                def debug(self, msg, *args, **kwargs):
                    self._log_with_component('debug', msg, *args, **kwargs)
                
                def info(self, msg, *args, **kwargs):
                    self._log_with_component('info', msg, *args, **kwargs)
                
                def warning(self, msg, *args, **kwargs):
                    self._log_with_component('warning', msg, *args, **kwargs)
                
                def error(self, msg, *args, **kwargs):
                    self._log_with_component('error', msg, *args, **kwargs)
                
                def critical(self, msg, *args, **kwargs):
                    self._log_with_component('critical', msg, *args, **kwargs)
            
            return ComponentLogger(logger, component)
        
        return logger
    
    def get_stats(self) -> Dict[str, Any]:
        """Get logging statistics."""
        stats = {
            "configured": self._configured,
            "log_directory": str(self._log_dir),
            "handlers": list(self._handlers.keys()),
            "log_files": []
        }
        
        # Get log file information
        if self._log_dir.exists():
            for log_file in self._log_dir.glob("*.log"):
                stats["log_files"].append({
                    "name": log_file.name,
                    "size": log_file.stat().st_size,
                    "modified": datetime.fromtimestamp(log_file.stat().st_mtime).isoformat()
                })
        
        return stats


# Global instances
_logging_manager: Optional[LoggingManager] = None
_request_context = RequestContext()


def get_logging_manager() -> LoggingManager:
    """Get the global logging manager."""
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
    return _logging_manager


def configure_logging(environment: str = "development") -> None:
    """Configure logging for the application."""
    manager = get_logging_manager()
    manager.configure_logging(environment)


def get_logger(name: str, component: str = None) -> logging.Logger:
    """Get a configured logger."""
    manager = get_logging_manager()
    return manager.get_logger(name, component)


@contextmanager
def request_context(request_id: str = None, component: str = None, operation: str = None):
    """
    Context manager for request tracking.
    
    Args:
        request_id: Request identifier
        component: Component name
        operation: Operation name
    """
    # Generate request ID if not provided
    if request_id is None:
        request_id = str(uuid.uuid4())[:8]
    
    # Store old context
    old_request_id = _request_context.request_id
    old_component = _request_context.component
    old_operation = _request_context.operation
    
    # Set new context
    _request_context.request_id = request_id
    if component:
        _request_context.component = component
    if operation:
        _request_context.operation = operation
    
    try:
        yield request_id
    finally:
        # Restore old context
        _request_context.request_id = old_request_id
        _request_context.component = old_component
        _request_context.operation = old_operation


@contextmanager
def operation_timer(logger: logging.Logger, operation_name: str):
    """
    Context manager for timing operations.
    
    Args:
        logger: Logger to use
        operation_name: Name of the operation
    """
    start_time = time.time()
    old_operation = _request_context.operation
    _request_context.operation = operation_name
    
    try:
        logger.info(f"Starting operation: {operation_name}")
        yield
        duration = (time.time() - start_time) * 1000
        logger.info(f"Completed operation: {operation_name}", extra={"duration_ms": duration})
    except Exception as e:
        duration = (time.time() - start_time) * 1000
        logger.error(f"Failed operation: {operation_name}", extra={"duration_ms": duration}, exc_info=True)
        raise
    finally:
        _request_context.operation = old_operation
