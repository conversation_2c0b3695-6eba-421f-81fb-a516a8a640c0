import logging
import os
import sys
import time
import json # Added for manual JSON dumping
import threading
import requests # For making HTTP requests
import atexit # For cleanup on exit
from typing import Dict, Any, List, Optional
from flask import Flask, render_template, jsonify, request, Response # Added request, Response
# from flask_compress import Compress # For gzip compression

# Add the current directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
3f34    sys.path.insert(0, current_dir)

# Import modules (try relative first, then absolute)
try:
    from .config_enhanced import load_config, get_enhanced_config
    from .modem_manager_new import get_modem_status, initialize_all_modems, shutdown_all_modem_readers
    from .database_manager import (
        init_db as initialize_database,
        get_all_messages,
        get_pending_forward_messages,
        update_forwarding_status,
        create_activation,
        get_activation_by_id,
        update_activation_details,
        find_active_activation_for_modem_service,
        get_open_activation_for_modem
    )
except ImportError:
    # Fallback to absolute imports when run directly
    from config_enhanced import load_config, get_enhanced_config
    from modem_manager_new import get_modem_status, initialize_all_modems, shutdown_all_modem_readers
    from database_manager import (
        init_db as initialize_database,
        get_all_messages,
        get_pending_forward_messages,
        update_forwarding_status,
        create_activation,
        get_activation_by_id,
        update_activation_details,
        find_active_activation_for_modem_service,
        get_open_activation_for_modem
    )

# Load configuration
config = load_config()

# Configure logging
log_level = config.get('log_level', 'INFO').upper()
logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Ensure modem_manager logger also respects the configured log_level
modem_manager_logger = logging.getLogger('smshub_app.app.modem_manager')
modem_manager_logger.setLevel(log_level)

# Get the directory containing main.py (smshub_app/app/)
current_dir = os.path.dirname(os.path.abspath(__file__))

# Construct absolute paths for templates and static folders
template_folder_abs = os.path.abspath(os.path.join(current_dir, '..', 'templates'))
static_folder_abs = os.path.abspath(os.path.join(current_dir, '..', 'static'))

app = Flask(__name__, template_folder=template_folder_abs, static_folder=static_folder_abs)
# Compress(app) # Initialize Flask-Compress for gzip support
logging.info("Flask application initialized with Compress.")
logging.info(f"Absolute template folder: {app.template_folder}")
logging.info(f"Absolute static folder: {app.static_folder}")
assert app.template_folder is not None, "Flask app.template_folder is unexpectedly None" 
expected_template_path = os.path.join(app.template_folder, 'dashboard.html')
logging.info(f"Checking for template at: {expected_template_path}, Exists: {os.path.exists(expected_template_path)}")

# --- SMS Forwarding Service (Our app PUSHING to SMSHub) ---
FORWARDING_ENABLED = config.get("sms_forwarding_enabled", False)
FORWARDING_URL = config.get("sms_forwarding_url")
AGENT_API_KEY = config.get("agent_api_key") 
FORWARDING_RETRY_DELAY = config.get("sms_forwarding_retry_delay_seconds", 10)
FORWARDING_MAX_ATTEMPTS = config.get("sms_forwarding_max_attempts", 5)
FORWARDING_SERVICE_INTERVAL = config.get("sms_forwarding_service_interval_seconds", 60)

SMSHUB_PROTOCOL_KEY = config.get("smshub_protocol_key")
if not SMSHUB_PROTOCOL_KEY:
    logger.critical("CRITICAL: smshub_protocol_key is not set in config.json. SMSHub Agent API will not work.")


def get_modem_phone_number_from_config(modem_port_or_id: str) -> Optional[str]:
    for modem_cfg in config.get("modems", []): 
        if modem_cfg.get("id") == modem_port_or_id or modem_cfg.get("port") == modem_port_or_id:
            if modem_cfg.get("phone_number"):
                return str(modem_cfg.get("phone_number"))
    logger.warning(f"Could not find phone number in static config for modem: {modem_port_or_id}")
    return None

def forward_sms_message(message: dict):
    if not FORWARDING_ENABLED or not FORWARDING_URL or not AGENT_API_KEY:
        logger.info(f"SMS Forwarding (to SMSHub) is disabled or not configured. Skipping message ID {message['id']}.")
        update_forwarding_status(message['id'], "SKIPPED", message['forwarding_attempts'])
        return

    logger.info(f"Attempting to forward SMS ID: {message['id']} from {message['sender']} on modem {message['modem_port']}.")
    active_activation = get_open_activation_for_modem(message['modem_port'])
    if not active_activation:
        logger.info(f"No open activation found for modem {message['modem_port']}. Skipping SMS ID {message['id']}.")
        update_forwarding_status(message['id'], "SKIPPED_NO_OPEN_ACTIVATION", message['forwarding_attempts'] + 1, "No open activation for this modem.")
        return

    activation_id = active_activation['id']
    activation_phone_number = active_activation['phone_number']
    
    try:
        # Ensure phone number is an integer and strip non-digits like '+'
        cleaned_phone_number = "".join(filter(str.isdigit, str(activation_phone_number)))
        phone_for_payload = int(cleaned_phone_number)
    except ValueError:
        logger.error(f"PUSH_SMS: Invalid phone number format for activation ID {activation_id}: {activation_phone_number}. Cannot convert to int.")
        update_forwarding_status(message['id'], "FAILED", message.get('forwarding_attempts', 0) + 1, "Invalid phone number format in activation.")
        return

    payload = {
        "action": "PUSH_SMS", "key": AGENT_API_KEY, "smsId": message['id'],
        "phone": phone_for_payload,
        "phoneFrom": message['sender'],
        "text": message['message_body']
    }
    headers = {"User-Agent": "SMSHubAgent/1.0", "Content-Type": "application/json"}
    current_attempts = message.get('forwarding_attempts', 0) + 1

    try:
        response = requests.post(FORWARDING_URL, json=payload, headers=headers, timeout=30)
        response.raise_for_status()
        response_data = response.json()
        if response_data.get("status") == "SUCCESS":
            logger.info(f"Successfully forwarded SMS ID {message['id']} to SMSHub for Activation ID {activation_id}.")
            update_forwarding_status(message['id'], "SUCCESS", current_attempts)
            update_activation_details(activation_id, our_status="SMS_FORWARDED_AWAITING_HUB")
        else:
            error_detail = response_data.get("error", "Unknown error from SMSHub API")
            logger.error(f"Failed to forward SMS ID {message['id']}. SMSHub API Error: {error_detail}")
            status_to_set = "FAILED"
            err_msg = f"Max attempts reached. API error: {error_detail}" if current_attempts >= FORWARDING_MAX_ATTEMPTS else f"API error: {error_detail}"
            update_forwarding_status(message['id'], status_to_set, current_attempts, err_msg)
    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP Request error forwarding SMS ID {message['id']} to SMSHub: {e}")
        status_to_set = "FAILED"
        err_msg = f"Max attempts reached. Request error: {str(e)}" if current_attempts >= FORWARDING_MAX_ATTEMPTS else f"Request error: {str(e)}"
        update_forwarding_status(message['id'], status_to_set, current_attempts, err_msg)
    except Exception as e:
        logger.error(f"Unexpected error forwarding SMS ID {message['id']} to SMSHub: {e}", exc_info=True)
        status_to_set = "FAILED"
        err_msg = f"Max attempts reached. Unexpected error: {str(e)}" if current_attempts >= FORWARDING_MAX_ATTEMPTS else f"Unexpected error: {str(e)}"
        update_forwarding_status(message['id'], status_to_set, current_attempts, err_msg)

def sms_forwarding_service():
    logger.info("SMS Forwarding Service (to SMSHub) started.")
    while True:
        if not FORWARDING_ENABLED:
            time.sleep(FORWARDING_SERVICE_INTERVAL)
            continue
        try:
            messages_to_forward = get_pending_forward_messages(limit=10)
            if messages_to_forward:
                logger.info(f"SMS Forwarding Service: Found {len(messages_to_forward)} messages to forward.")
                for message in messages_to_forward:
                    if message['forwarding_attempts'] < FORWARDING_MAX_ATTEMPTS:
                        forward_sms_message(message)
                        time.sleep(0.5)
                    elif message['forwarding_status'] != "FAILED":
                        update_forwarding_status(message['id'], "FAILED", message['forwarding_attempts'], "Max attempts reached prior to this cycle.")
        except Exception as e:
            logger.error(f"SMS Forwarding Service (to SMSHub) encountered an error: {e}", exc_info=True)
        time.sleep(FORWARDING_SERVICE_INTERVAL)

# --- SMSHub Agent API (SMSHub calling US) ---

def handle_get_services(data: Dict[str, Any]) -> Dict[str, Any]:
    logger.info(f"Handling GET_SERVICES request: {data}")
    
    service_country_name = config.get("service_country_name", "unknown_country")
    service_operator_name = config.get("service_operator_name", "unknown_operator")

    configured_services = config.get("services", {})
    dynamic_modem_statuses: List[Dict[str, Any]] = []
    try:
        dynamic_modem_statuses = get_modem_status()
    except Exception as e_modem_status:
        logger.error(f"Error encountered in get_modem_status() during GET_SERVICES: {e_modem_status}", exc_info=True)
        # Proceed with an empty list of modem statuses, will result in 0 services available
    
    total_active_modems_with_phone = 0
    
    for dyn_modem in dynamic_modem_statuses:
        modem_id = dyn_modem.get("id", "UnknownID") 
        status_val = dyn_modem.get("status")
        phone_number_val = dyn_modem.get("phone_number")

        is_responsive = status_val == "RESPONSIVE"
        
        has_phone_number = False
        if isinstance(phone_number_val, str):
            stripped_phone = phone_number_val.strip()
            if stripped_phone and \
               stripped_phone != "N/A" and \
               stripped_phone != "UNKNOWN" and \
               "ERROR" not in stripped_phone.upper() and \
               len(stripped_phone) > 5: 
                has_phone_number = True
        
        logger.info(f"GET_SERVICES Check - Modem ID: {modem_id}, Raw Status: '{status_val}', Raw Phone: '{phone_number_val}', Evaluated has_phone_number: {has_phone_number}")
        
        if is_responsive and has_phone_number:
            total_active_modems_with_phone += 1
            logger.info(f"GET_SERVICES: Modem ID: {modem_id} counted. total_active_modems_with_phone is now {total_active_modems_with_phone}")
        else:
            logger.info(f"GET_SERVICES: Modem ID: {modem_id} NOT counted. Responsive: {is_responsive}, HasPhone: {has_phone_number}")

    service_availability_map: Dict[str, int] = {}
    if total_active_modems_with_phone > 0:
        for service_key, is_enabled_in_config in configured_services.items():
            if is_enabled_in_config: 
                service_availability_map[service_key] = total_active_modems_with_phone
    
    country_list_response = [{
        "country": service_country_name,
        "operatorMap": {
            service_operator_name: service_availability_map
        }
    }]
    
    response_to_send = {"status": "SUCCESS", "countryList": country_list_response}
    logger.info(f"GET_SERVICES: Full response being sent: {response_to_send}") # Log the full response
    return response_to_send

def handle_get_number(data: Dict[str, Any]) -> Dict[str, Any]:
    logger.info(f"Handling GET_NUMBER request: {data}")
    req_country_name = data.get("country") 
    req_service = data.get("service")
    req_operator_name = data.get("operator") 
    exception_phone_set = data.get("exceptionPhoneSet", [])
    
    config_service_country = config.get("service_country_name")
    config_service_operator = config.get("service_operator_name")

    if req_country_name != config_service_country or req_operator_name != config_service_operator:
        logger.warning(f"GET_NUMBER: Requested country '{req_country_name}' or operator '{req_operator_name}' does not match configured '{config_service_country}/{config_service_operator}'.")
        return {"status": "NO_NUMBERS", "error": "Country/Operator mismatch with agent configuration"}

    if not req_service:
        logger.warning(f"GET_NUMBER: Missing required field: service. Request: {data}")
        return {"status": "ERROR", "error": "Missing service"}

    if req_service not in config.get("services", {}) or not config["services"][req_service]:
        logger.warning(f"GET_NUMBER: Requested service '{req_service}' is not enabled in global services config.")
        return {"status": "NO_NUMBERS", "error": f"Service {req_service} not enabled"}
    
    dynamic_modem_statuses = get_modem_status()

    for dyn_modem in dynamic_modem_statuses:
        modem_id = dyn_modem.get("id")
        if not modem_id:
            logger.debug("Skipping modem with no ID in dynamic_modem_statuses for GET_NUMBER.")
            continue

        is_responsive = dyn_modem.get("status") == "RESPONSIVE"
        modem_phone_to_use = dyn_modem.get("phone_number")

        has_valid_phone_number = isinstance(modem_phone_to_use, str) and \
                                 modem_phone_to_use.strip() and \
                                 modem_phone_to_use != "N/A" and \
                                 modem_phone_to_use != "UNKNOWN" and \
                                 "ERROR" not in modem_phone_to_use.upper() and \
                                 len(modem_phone_to_use) > 5 # Basic check for some digits

        if not is_responsive:
            logger.debug(f"Modem {modem_id} is not responsive. Skipping for GET_NUMBER.")
            continue
        
        if not has_valid_phone_number:
            logger.debug(f"Modem {modem_id} has no valid phone number ('{modem_phone_to_use}'). Skipping for GET_NUMBER.")
            continue

        # Check if this phone number is in the exception list
        is_excluded = any(str(modem_phone_to_use).startswith(prefix) for prefix in exception_phone_set)
        if is_excluded:
            logger.info(f"Modem {modem_id} with phone {modem_phone_to_use} is in exceptionPhoneSet. Skipping.")
            continue

        # Check if this modem is already active for the requested service
        active_activation = find_active_activation_for_modem_service(modem_id, req_service)
        if active_activation:
            logger.info(f"Modem {modem_id} (Phone: {modem_phone_to_use}) is already active for service {req_service} (Activation ID: {active_activation['id']}). Skipping.")
            continue

        # Use global country and operator for activation
        activation_country = config.get("service_country_name")
        activation_operator = config.get("service_operator_name")

        activation_id = create_activation(
            modem_port=modem_id,
            phone_number=str(modem_phone_to_use), # Ensure it's a string for DB
            service=req_service,
            country=activation_country,
            operator=activation_operator
        )

        if activation_id:
            logger.info(f"Issued number {modem_phone_to_use} (Modem: {modem_id}) for service {req_service}. Activation ID: {activation_id}")
            try:
                # Ensure the phone number is cleaned for the API response (integer)
                cleaned_phone_for_api = "".join(filter(str.isdigit, str(modem_phone_to_use)))
                if not cleaned_phone_for_api: # Handle case where phone number becomes empty after stripping
                    raise ValueError("Phone number became empty after stripping non-digits")
                return {"status": "SUCCESS", "number": int(cleaned_phone_for_api), "activationId": activation_id}
            except ValueError as e:
                logger.error(f"Phone number {modem_phone_to_use} for modem {modem_id} is not a valid integer after cleaning or was invalid: {e}")
                # Attempt to cancel the activation we just created due to this error
                update_activation_details(activation_id, our_status="CANCELLED_INTERNAL_ERROR", smshub_status=-1) # -1 indicates an internal error before SMSHub interaction
                # Continue to the next modem, as this one had an issue
                continue
        else:
            logger.error(f"Failed to create activation record for modem {modem_id} (Phone: {modem_phone_to_use}), service {req_service}.")
            # No activation_id means we don't need to cancel anything here.

    logger.warning(f"No suitable and available numbers found after checking all dynamic modems for GET_NUMBER request: {data}")
    return {"status": "NO_NUMBERS"}

def handle_finish_activation(data: Dict[str, Any]) -> Dict[str, Any]:
    logger.info(f"Handling FINISH_ACTIVATION request: {data}")
    activation_id = data.get("activationId")
    smshub_status_code = data.get("status")

    if activation_id is None or smshub_status_code is None:
        return {"status": "ERROR", "error": "Missing activationId or status"}

    activation = get_activation_by_id(activation_id)
    if not activation:
        logger.warning(f"FINISH_ACTIVATION: Activation ID {activation_id} not found.")
        return {"status": "ERROR", "error": f"Activation ID {activation_id} not found"}

    if activation.get('smshub_reported_status') == smshub_status_code and activation.get('status_in_our_system') != 'PENDING_SMS':
        logger.info(f"FINISH_ACTIVATION: Activation ID {activation_id} already processed with SMSHub status {smshub_status_code}. Idempotent success.")
        return {"status": "SUCCESS"}

    our_new_status = activation['status_in_our_system']
    if smshub_status_code == 1: our_new_status = "CANCELLED_SMSHUB_NOLONGERPROVIDE"
    elif smshub_status_code == 3: our_new_status = "COMPLETED_SMSHUB_SOLD"
    elif smshub_status_code == 4: our_new_status = "CANCELLED_SMSHUB_BADNUMBER"
    elif smshub_status_code == 5: our_new_status = "CANCELLED_SMSHUB_REFUND"

    if update_activation_details(activation_id, smshub_status=smshub_status_code, our_status=our_new_status):
        logger.info(f"FINISH_ACTIVATION: Successfully processed for Activation ID {activation_id}, SMSHub status {smshub_status_code}.")
        return {"status": "SUCCESS"}
    else:
        logger.error(f"FINISH_ACTIVATION: Failed to update database for Activation ID {activation_id}.")
        return {"status": "ERROR", "error": "Database update failed"}


@app.route('/api/smshub_agent', methods=['POST'])
def smshub_agent_handler():
    logger.info(f"SMSHub Agent endpoint /api/smshub_agent hit. Headers: {request.headers}")
    try:
        raw_data_for_logging = request.data
        logger.debug(f"Raw request data: {raw_data_for_logging[:500]}")

        if not request.is_json:
            logger.warning(f"Request Content-Type is not application/json. Headers: {request.headers}")
            return Response(json.dumps({"status": "ERROR", "error": "Content-Type must be application/json"}), status=415, mimetype='application/json')

        data = request.get_json()
        if not data:
            logger.error("JSON payload could not be parsed or is empty, though Content-Type was application/json.")
            return Response(json.dumps({"status": "ERROR", "error": "Invalid or empty JSON payload"}), status=400, mimetype='application/json')

        logger.info(f"Received JSON request for SMSHub Agent: {data}")
        action = data.get("action")
        key = data.get("key")

        if not SMSHUB_PROTOCOL_KEY:
            logger.error("SMSHUB_PROTOCOL_KEY is not configured on the server. Cannot validate client key.")
            return Response(json.dumps({"status": "ERROR", "error": "Server configuration error: protocol key not set"}), status=500, mimetype='application/json')

        if key != SMSHUB_PROTOCOL_KEY:
            logger.warning(f"Invalid protocol key received: {key}")
            return Response(json.dumps({"status": "ERROR", "error": "Invalid protocol key"}), status=403, mimetype='application/json')

        response_data: Dict[str, Any]
        if action == "GET_SERVICES":
            response_data = handle_get_services(data)
        elif action == "GET_NUMBER":
            response_data = handle_get_number(data)
        elif action == "FINISH_ACTIVATION":
            response_data = handle_finish_activation(data)
        else:
            logger.warning(f"Unknown action received: {action}")
            response_data = {"status": "ERROR", "error": f"Unknown action: {action}"}
        logger.info(f"SMSHub Agent: Final response_data before sending: {response_data}")
        return Response(json.dumps(response_data), mimetype='application/json')
    except Exception as e:
        logger.error(f"Error in SMSHub Agent handler: {e}", exc_info=True)
        return Response(json.dumps({"status": "ERROR", "error": f"An internal error occurred: {str(e)}"}), status=500, mimetype='application/json')

# --- End SMSHub Agent API ---

print("DEBUG: main.py - Top of module execution (before initializers)") # DEBUG PRINT
try:
    logger.info("Application module loading - initializing database...")
    initialize_database()
    logger.info("Database initialization call complete.")
    logger.info("Application module loading - initializing modems...")
    initialize_all_modems()
    logger.info("Modem initialization call complete.")
    # NOTE: Removed old SMS polling system - new system uses AT+CNMI notifications automatically
    logger.info("SMS monitoring is handled automatically by the new modem manager with AT+CNMI notifications")
except Exception as e_init:
    logger.critical(f"CRITICAL ERROR during module-level initialization: {e_init}", exc_info=True)
    print(f"CRITICAL ERROR during module-level initialization: {e_init}") # DEBUG PRINT
    # Optionally, re-raise or sys.exit(1) if this is fatal for app startup
print("DEBUG: main.py - After initializers, before Flask routes") # DEBUG PRINT

@app.route('/')
def home_redirect(): return render_template('dashboard.html')
@app.route('/dashboard')
def dashboard():
    logging.info("Dashboard accessed.")
    return render_template('dashboard.html')
@app.route('/api/modem_status')
def api_modem_status():
    logging.debug("API /api/modem_status called")
    return jsonify(get_modem_status())
@app.route('/api/sms_messages')
def api_sms_messages():
    logger.debug("API /api/sms_messages called")
    try:
        messages = get_all_messages(limit=100)
        modem_statuses = get_modem_status() # Get current modem statuses

        # Create a lookup map for modem port to phone number
        modem_phone_map = {modem.get('port'): modem.get('phone_number', 'N/A')
                           for modem in modem_statuses if modem.get('port')}

        enriched_messages = []
        for msg_row in messages: # Assuming get_all_messages returns a list of Row-like objects or dicts
            msg_dict = dict(msg_row) # Ensure it's a dictionary for modification
            modem_port = msg_dict.get('modem_port')
            msg_dict['receiving_modem_phone_number'] = modem_phone_map.get(modem_port, 'N/A')
            enriched_messages.append(msg_dict)
        
        return jsonify(enriched_messages)
    except Exception as e:
        logger.error(f"Error in /api/sms_messages: {e}", exc_info=True)
        return jsonify({"error": "Failed to retrieve SMS messages", "details": str(e)}), 500

print("DEBUG: main.py - Entering __main__ block check") # DEBUG PRINT
if __name__ == '__main__':
    print("DEBUG: main.py - Inside __main__ block") # DEBUG PRINT
    if FORWARDING_ENABLED:
        forwarding_thread = threading.Thread(target=sms_forwarding_service, daemon=True)
        forwarding_thread.start()
        logger.info("SMS Forwarding Service (to SMSHub) thread started.")
    else: logger.info("SMS Forwarding Service (to SMSHub) is disabled by configuration.")
    
    # Register modem shutdown
    atexit.register(shutdown_all_modem_readers)
    logger.info("Registered modem shutdown function with atexit.")

    app_host = config.get('app_host', '0.0.0.0')
    app_port = config.get('app_port', 5000)
    logger.info(f"Starting SMSHub App on {app_host}:{app_port}")
    print(f"DEBUG: main.py - About to call app.run() on {app_host}:{app_port}") # DEBUG PRINT
    try:
        app.run(debug=True, host=app_host, port=app_port, use_reloader=False)
    except Exception as e_app_run:
        logger.critical(f"CRITICAL ERROR during app.run(): {e_app_run}", exc_info=True)
        print(f"CRITICAL ERROR during app.run(): {e_app_run}") # DEBUG PRINT
    print("DEBUG: main.py - After app.run() (should not be reached if server runs indefinitely)") # DEBUG PRINT
else:
    # For WSGI servers, atexit might also work, but robust cleanup often depends on the WSGI server's lifecycle hooks.
    # Registering it here is a good first step.
    if not hasattr(app, '_atexit_shutdown_registered'): # Ensure it's registered only once
        atexit.register(shutdown_all_modem_readers)
        setattr(app, '_atexit_shutdown_registered', True)
        logger.info("Registered modem shutdown function with atexit (WSGI context).")
    print("DEBUG: main.py - NOT in __main__ block (likely WSGI context)") # DEBUG PRINT
    if FORWARDING_ENABLED and not app.debug: # Check app.debug for WSGI context as well
        if not hasattr(app, '_forwarding_thread_started'): # Ensure thread only starts once
            wsgi_forwarding_thread = threading.Thread(target=sms_forwarding_service, daemon=True)
            wsgi_forwarding_thread.start()
            setattr(app, '_forwarding_thread_started', True)
            logger.info("SMS Forwarding Service (to SMSHub) thread started (WSGI context).")
    elif not FORWARDING_ENABLED:
        logger.info("SMS Forwarding Service (to SMSHub) is disabled (WSGI context).")