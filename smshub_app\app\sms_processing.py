"""
SMS processing module for the SMSHub application.

This module handles SMS message parsing, processing, and management
for different modem types and message formats.
"""

import logging
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class SMSMessage:
    """Represents an SMS message with all relevant metadata."""
    
    def __init__(self, index: int = None, status: str = None, sender: str = None, 
                 timestamp: str = None, message_body: str = None, modem_port: str = None):
        self.index = index
        self.status = status
        self.sender = sender
        self.timestamp = timestamp
        self.message_body = message_body
        self.modem_port = modem_port
        self.parsed_timestamp = None
        
        if timestamp:
            self.parsed_timestamp = self._parse_timestamp(timestamp)
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse timestamp string into datetime object."""
        try:
            # Common timestamp formats from modems
            formats = [
                "%y/%m/%d,%H:%M:%S%z",  # 23/12/25,14:30:45+00
                "%Y/%m/%d,%H:%M:%S%z",  # 2023/12/25,14:30:45+00
                "%y/%m/%d,%H:%M:%S",    # 23/12/25,14:30:45
                "%Y/%m/%d,%H:%M:%S",    # 2023/12/25,14:30:45
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(timestamp_str, fmt)
                except ValueError:
                    continue
            
            logger.warning(f"Could not parse timestamp: {timestamp_str}")
            return None
        except Exception as e:
            logger.error(f"Error parsing timestamp {timestamp_str}: {e}")
            return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert SMS message to dictionary."""
        return {
            "index": self.index,
            "status": self.status,
            "sender": self.sender,
            "timestamp": self.timestamp,
            "message_body": self.message_body,
            "modem_port": self.modem_port,
            "parsed_timestamp": self.parsed_timestamp.isoformat() if self.parsed_timestamp else None
        }
    
    def __str__(self) -> str:
        return f"SMS(from={self.sender}, to={self.modem_port}, body='{self.message_body[:50]}...')"


class SMSParser:
    """Handles parsing of SMS messages from different modem response formats."""
    
    @staticmethod
    def parse_cmgl_response(response_lines: List[str], modem_port: str) -> List[SMSMessage]:
        """
        Parse CMGL (list messages) response from modem.
        
        Args:
            response_lines: Raw response lines from AT+CMGL command
            modem_port: Port identifier for the modem
            
        Returns:
            List of parsed SMS messages
        """
        messages = []
        current_message = None
        
        try:
            for line in response_lines:
                line = line.strip()
                if not line or line in ["OK", "ERROR"]:
                    continue
                
                # Check for message header: +CMGL: index,status,sender,timestamp
                if line.startswith("+CMGL:"):
                    # Save previous message if exists
                    if current_message and current_message.message_body:
                        messages.append(current_message)
                    
                    # Parse header
                    current_message = SMSParser._parse_cmgl_header(line, modem_port)
                
                elif current_message:
                    # This is message body content
                    if current_message.message_body:
                        current_message.message_body += "\n" + line
                    else:
                        current_message.message_body = line
            
            # Don't forget the last message
            if current_message and current_message.message_body:
                messages.append(current_message)
                
        except Exception as e:
            logger.error(f"Error parsing CMGL response: {e}")
        
        logger.info(f"Parsed {len(messages)} SMS messages from CMGL response")
        return messages
    
    @staticmethod
    def _parse_cmgl_header(header_line: str, modem_port: str) -> SMSMessage:
        """Parse a CMGL header line."""
        try:
            # Remove +CMGL: prefix and split by comma
            content = header_line[6:].strip()  # Remove "+CMGL:"
            
            # Handle quoted fields properly
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in content:
                if char == '"' and (not current_part or current_part[-1] != '\\'):
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(current_part.strip('"'))
                    current_part = ""
                    continue
                current_part += char
            
            if current_part:
                parts.append(current_part.strip('"'))
            
            # Extract fields
            index = int(parts[0]) if len(parts) > 0 and parts[0].isdigit() else None
            status = parts[1] if len(parts) > 1 else "UNKNOWN"
            sender = parts[2] if len(parts) > 2 else "UNKNOWN"
            timestamp = parts[4] if len(parts) > 4 else None  # Skip parts[3] which is usually empty
            
            return SMSMessage(
                index=index,
                status=status,
                sender=sender,
                timestamp=timestamp,
                modem_port=modem_port
            )
            
        except Exception as e:
            logger.error(f"Error parsing CMGL header '{header_line}': {e}")
            return SMSMessage(modem_port=modem_port)
    
    @staticmethod
    def parse_cmt_notification(notification_data: str, modem_port: str) -> Optional[SMSMessage]:
        """
        Parse CMT (new message notification) from modem.
        
        Args:
            notification_data: Raw notification data
            modem_port: Port identifier for the modem
            
        Returns:
            Parsed SMS message or None if parsing failed
        """
        try:
            lines = notification_data.strip().split('\n')
            header = None
            message_body = ""
            
            for i, line in enumerate(lines):
                if '+CMT:' in line:
                    header = line
                    # Message text starts from the next line
                    message_body = '\n'.join(lines[i+1:]).strip()
                    break
            
            if not header:
                logger.warning(f"Could not find CMT header in notification: {notification_data}")
                return None
            
            # Parse header: +CMT: "sender",,"timestamp"
            header_content = header.split(':', 1)[1].strip()
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in header_content:
                if char == '"' and (not current_part or current_part[-1] != '\\'):
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(current_part.strip('"'))
                    current_part = ""
                    continue
                current_part += char
            
            if current_part:
                parts.append(current_part.strip('"'))
            
            sender = parts[0] if len(parts) > 0 else "UNKNOWN"
            timestamp = parts[2] if len(parts) > 2 else None
            
            return SMSMessage(
                status="UNREAD",
                sender=sender,
                timestamp=timestamp,
                message_body=message_body,
                modem_port=modem_port
            )
            
        except Exception as e:
            logger.error(f"Error parsing CMT notification: {e}")
            return None


class SMSProcessor:
    """Handles SMS processing operations including reading, sending, and management."""
    
    def __init__(self, modem_communicator):
        """
        Initialize SMS processor.
        
        Args:
            modem_communicator: ModemCommunicator instance
        """
        self.communicator = modem_communicator
        self.parser = SMSParser()
    
    def set_text_mode(self) -> bool:
        """
        Set SMS to text mode (AT+CMGF=1).
        
        Returns:
            True if successful, False otherwise
        """
        try:
            success, response = self.communicator.send_at_command("AT+CMGF=1", timeout_override=3.0)
            if success:
                logger.info(f"SMS text mode set successfully on {self.communicator.port}")
                return True
            else:
                logger.error(f"Failed to set SMS text mode on {self.communicator.port}: {response}")
                return False
        except Exception as e:
            logger.error(f"Error setting SMS text mode on {self.communicator.port}: {e}")
            return False
    
    def configure_notifications(self, mode: int = 2, mt: int = 1) -> bool:
        """
        Configure SMS notifications (AT+CNMI).
        
        Args:
            mode: CNMI mode parameter
            mt: CNMI mt parameter
            
        Returns:
            True if successful, False otherwise
        """
        try:
            command = f"AT+CNMI={mode},{mt},0,0,0"
            success, response = self.communicator.send_at_command(command, timeout_override=5.0)
            
            if success:
                logger.info(f"SMS notifications configured on {self.communicator.port}: mode={mode}, mt={mt}")
                return True
            else:
                logger.error(f"Failed to configure SMS notifications on {self.communicator.port}: {response}")
                return False
        except Exception as e:
            logger.error(f"Error configuring SMS notifications on {self.communicator.port}: {e}")
            return False
    
    def read_all_messages(self) -> List[SMSMessage]:
        """
        Read all SMS messages from the modem.
        
        Returns:
            List of SMS messages
        """
        try:
            success, response = self.communicator.send_at_command("AT+CMGL=\"ALL\"", timeout_override=10.0)
            
            if success:
                messages = self.parser.parse_cmgl_response(response, self.communicator.port)
                logger.info(f"Read {len(messages)} SMS messages from {self.communicator.port}")
                return messages
            else:
                logger.warning(f"Failed to read SMS messages from {self.communicator.port}: {response}")
                return []
                
        except Exception as e:
            logger.error(f"Error reading SMS messages from {self.communicator.port}: {e}")
            return []
    
    def send_message(self, phone_number: str, message: str) -> bool:
        """
        Send an SMS message.
        
        Args:
            phone_number: Destination phone number
            message: Message text to send
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Set text mode first
            if not self.set_text_mode():
                return False
            
            # Start SMS composition
            command = f'AT+CMGS="{phone_number}"'
            success, response = self.communicator.send_at_command(command, expected_response=">", timeout_override=10.0)
            
            if not success:
                logger.error(f"Failed to start SMS composition on {self.communicator.port}: {response}")
                return False
            
            # Send message text followed by Ctrl+Z
            message_with_terminator = message + '\x1A'  # Ctrl+Z
            success, response = self.communicator.send_at_command(
                message_with_terminator, 
                expected_response="OK", 
                timeout_override=30.0
            )
            
            if success:
                logger.info(f"SMS sent successfully from {self.communicator.port} to {phone_number}")
                return True
            else:
                logger.error(f"Failed to send SMS from {self.communicator.port}: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending SMS from {self.communicator.port}: {e}")
            return False
    
    def delete_message(self, index: int) -> bool:
        """
        Delete an SMS message by index.
        
        Args:
            index: Message index to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            command = f"AT+CMGD={index}"
            success, response = self.communicator.send_at_command(command, timeout_override=5.0)
            
            if success:
                logger.info(f"Deleted SMS message {index} from {self.communicator.port}")
                return True
            else:
                logger.warning(f"Failed to delete SMS message {index} from {self.communicator.port}: {response}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting SMS message {index} from {self.communicator.port}: {e}")
            return False
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        Get SMS storage information.
        
        Returns:
            Dictionary with storage information
        """
        try:
            success, response = self.communicator.send_at_command("AT+CPMS?", timeout_override=5.0)
            
            if success:
                # Parse response: +CPMS: "SM",used,total,"SM",used,total,"SM",used,total
                for line in response:
                    if "+CPMS:" in line:
                        # Extract storage info
                        content = line.split(':', 1)[1].strip()
                        # Simple parsing - could be enhanced
                        return {"raw_response": content, "status": "success"}
                
            return {"status": "failed", "response": response}
            
        except Exception as e:
            logger.error(f"Error getting SMS storage info from {self.communicator.port}: {e}")
            return {"status": "error", "error": str(e)}
