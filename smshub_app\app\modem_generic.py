"""
Generic modem implementation for the SMSHub application.

This module provides a generic modem implementation that works
with most AT command compatible modems.
"""

import logging
import re
from typing import Dict, Any, List, Tuple, Optional

try:
    from .modem_base import BaseModem, ModemConfig, ModemInfo
    from .modem_communication import ModemCommunicator, execute_modem_command
    from .sms_processing import SMSProcessor
    from .error_handling import with_error_handling, ErrorCategory, ErrorSeverity
except ImportError:
    from modem_base import BaseModem, ModemConfig, ModemInfo
    from modem_communication import ModemCommunicator, execute_modem_command
    from sms_processing import SMSProcessor
    from error_handling import with_error_handling, ErrorCategory, ErrorSeverity

logger = logging.getLogger(__name__)


class GenericModem(BaseModem):
    """
    Generic modem implementation for AT command compatible modems.
    
    This class provides a standard implementation that should work
    with most cellular modems that support AT commands.
    """

    def __init__(self, port: str, config: ModemConfig = None):
        """Initialize the generic modem."""
        super().__init__(port, config)
        self.communicator: Optional[ModemCommunicator] = None
        self.sms_processor: Optional[SMSProcessor] = None
        self.model_hint = "Generic AT Modem"

    @with_error_handling("GenericModem", ErrorCategory.COMMUNICATION, ErrorSeverity.HIGH)
    def connect(self) -> bool:
        """Establish connection to the modem."""
        try:
            if self._is_connected:
                return True

            self.communicator = ModemCommunicator(
                port=self.port,
                baudrate=self.config.baudrate,
                timeout=self.config.timeout
            )

            if self.communicator.connect():
                self.sms_processor = SMSProcessor(self.communicator)
                self._is_connected = True
                self._info.status = "CONNECTED"
                self._info.connection_details = f"Connected at {self.config.baudrate} baud"
                logger.info(f"Successfully connected to generic modem on {self.port}")
                return True
            else:
                self._last_error = "Failed to establish serial connection"
                return False

        except Exception as e:
            self._last_error = str(e)
            logger.error(f"Error connecting to modem on {self.port}: {e}")
            return False

    @with_error_handling("GenericModem", ErrorCategory.COMMUNICATION, ErrorSeverity.MEDIUM)
    def disconnect(self) -> bool:
        """Disconnect from the modem."""
        try:
            if self.communicator:
                result = self.communicator.disconnect()
                self.communicator = None
                self.sms_processor = None
                self._is_connected = False
                self._info.status = "DISCONNECTED"
                return result
            return True
        except Exception as e:
            self._last_error = str(e)
            logger.error(f"Error disconnecting from modem on {self.port}: {e}")
            return False

    def send_at_command(self, command: str, expected_response: str = "OK", 
                       timeout: Optional[float] = None) -> Tuple[bool, List[str]]:
        """Send an AT command to the modem."""
        if not self.communicator:
            return False, ["ERROR: Not connected"]
        
        return self.communicator.send_at_command(command, expected_response, timeout)

    def get_signal_quality(self) -> Tuple[bool, Dict[str, Any]]:
        """Get signal quality information."""
        def parse_signal_quality(response: List[str]) -> Dict[str, Any]:
            for line in response:
                if "+CSQ:" in line:
                    # Parse: +CSQ: rssi,ber
                    match = re.search(r'\+CSQ:\s*(\d+),(\d+)', line)
                    if match:
                        rssi_raw = int(match.group(1))
                        ber = int(match.group(2))
                        
                        # Convert RSSI to dBm
                        if rssi_raw == 99:
                            rssi_dbm = "Unknown"
                        else:
                            rssi_dbm = -113 + (rssi_raw * 2)
                        
                        return {
                            "rssi": rssi_dbm,
                            "rssi_raw": rssi_raw,
                            "ber": ber,
                            "signal_strength": f"{rssi_dbm} dBm" if rssi_dbm != "Unknown" else "Unknown"
                        }
            return {"rssi": "Unknown", "ber": "Unknown", "signal_strength": "Unknown"}

        return execute_modem_command(
            self.communicator, "get_signal_quality", "AT+CSQ", "+CSQ:", 
            parse_signal_quality, 3.0
        )

    def get_network_operator(self) -> Tuple[bool, str]:
        """Get network operator information."""
        def parse_operator(response: List[str]) -> str:
            for line in response:
                if "+COPS:" in line:
                    # Parse: +COPS: mode,format,"operator"
                    match = re.search(r'\+COPS:\s*\d+,\d+,"([^"]*)"', line)
                    if match:
                        return match.group(1)
            return "Unknown"

        return execute_modem_command(
            self.communicator, "get_network_operator", "AT+COPS?", "+COPS:", 
            parse_operator, 5.0
        )

    def get_phone_number(self) -> Tuple[bool, str]:
        """Get modem phone number."""
        def parse_phone_number(response: List[str]) -> str:
            for line in response:
                if "+CNUM:" in line:
                    # Parse: +CNUM: "name","number",type
                    match = re.search(r'\+CNUM:\s*"[^"]*","([^"]*)"', line)
                    if match:
                        number = match.group(1)
                        if number and number != "":
                            return number
            return "Unknown"

        return execute_modem_command(
            self.communicator, "get_phone_number", "AT+CNUM", "+CNUM:", 
            parse_phone_number, 5.0
        )

    def get_imei(self) -> Tuple[bool, str]:
        """Get modem IMEI."""
        def parse_imei(response: List[str]) -> str:
            for line in response:
                # IMEI is usually returned as a plain number
                if line.isdigit() and len(line) == 15:
                    return line
                # Some modems return it with +CGSN: prefix
                if "+CGSN:" in line:
                    match = re.search(r'\+CGSN:\s*(\d{15})', line)
                    if match:
                        return match.group(1)
            return "Unknown"

        return execute_modem_command(
            self.communicator, "get_imei", "AT+CGSN", "OK", 
            parse_imei, 3.0
        )

    def get_model(self) -> Tuple[bool, str]:
        """Get modem model information."""
        def parse_model(response: List[str]) -> str:
            for line in response:
                if line and line not in ["OK", "ERROR", ""]:
                    # Skip lines that look like command echoes
                    if not line.startswith("AT"):
                        return line.strip()
            return "Unknown"

        return execute_modem_command(
            self.communicator, "get_model", "ATI", "OK", 
            parse_model, 3.0
        )

    def get_sim_status(self) -> Tuple[bool, str]:
        """Get SIM card status."""
        def parse_sim_status(response: List[str]) -> str:
            for line in response:
                if "+CPIN:" in line:
                    # Parse: +CPIN: status
                    match = re.search(r'\+CPIN:\s*(.+)', line)
                    if match:
                        status = match.group(1).strip()
                        return status
            return "Unknown"

        return execute_modem_command(
            self.communicator, "get_sim_status", "AT+CPIN?", "+CPIN:", 
            parse_sim_status, 3.0
        )

    def set_sms_text_mode(self) -> bool:
        """Set SMS to text mode."""
        if not self.sms_processor:
            return False
        return self.sms_processor.set_text_mode()

    def configure_sms_notifications(self, mode: int = 2, mt: int = 1) -> bool:
        """Configure SMS notification settings."""
        if not self.sms_processor:
            return False
        return self.sms_processor.configure_notifications(mode, mt)

    def read_sms_messages(self) -> List[Dict[str, Any]]:
        """Read SMS messages from the modem."""
        if not self.sms_processor:
            return []
        
        messages = self.sms_processor.read_all_messages()
        return [msg.to_dict() for msg in messages]

    def send_sms(self, phone_number: str, message: str) -> bool:
        """Send an SMS message."""
        if not self.sms_processor:
            return False
        return self.sms_processor.send_message(phone_number, message)

    def initialize_for_sms(self) -> bool:
        """Initialize the modem for SMS operations."""
        try:
            if not self.is_connected:
                logger.error(f"Cannot initialize SMS - modem {self.port} not connected")
                return False

            # Set SMS text mode
            if not self.set_sms_text_mode():
                logger.error(f"Failed to set SMS text mode on {self.port}")
                return False

            # Configure SMS notifications
            if not self.configure_sms_notifications():
                logger.error(f"Failed to configure SMS notifications on {self.port}")
                return False

            logger.info(f"Successfully initialized SMS operations on {self.port}")
            return True

        except Exception as e:
            logger.error(f"Error initializing SMS on {self.port}: {e}")
            return False

    def get_detailed_info(self) -> Dict[str, Any]:
        """Get detailed modem information."""
        info = {
            "port": self.port,
            "status": self._info.status if self.is_connected else "DISCONNECTED",
            "model": "Unknown",
            "imei": "Unknown",
            "phone_number": "Unknown",
            "sim_status": "Unknown",
            "signal_quality": "Unknown",
            "operator": "Unknown"
        }

        # Always try to get cached info from _info first
        if hasattr(self._info, 'model') and self._info.model and self._info.model not in ["N/A", "Unknown", ""]:
            info["model"] = self._info.model
        if hasattr(self._info, 'imei') and self._info.imei and self._info.imei not in ["N/A", "Unknown", ""]:
            info["imei"] = self._info.imei
        if hasattr(self._info, 'phone_number') and self._info.phone_number and self._info.phone_number not in ["N/A", "Unknown", ""]:
            info["phone_number"] = self._info.phone_number
        if hasattr(self._info, 'sim_status') and self._info.sim_status and self._info.sim_status not in ["N/A", "Unknown", ""]:
            info["sim_status"] = self._info.sim_status
        if hasattr(self._info, 'operator') and self._info.operator and self._info.operator not in ["N/A", "Unknown", ""]:
            info["operator"] = self._info.operator

        # If modem is not connected, return cached info
        if not self.is_connected:
            return info

        try:
            # Get all information from connected modem
            success, model = self.get_model()
            if success and model and model != "Unknown":
                info["model"] = model
                # Cache the model info
                self._info.model = model

            success, imei = self.get_imei()
            if success and imei and imei != "Unknown":
                info["imei"] = imei
                # Cache the IMEI
                self._info.imei = imei

            success, phone = self.get_phone_number()
            if success and phone and phone != "Unknown":
                info["phone_number"] = phone
                # Cache the phone number
                self._info.phone_number = phone

            success, sim_status = self.get_sim_status()
            if success:
                info["sim_status"] = sim_status

            success, signal_info = self.get_signal_quality()
            if success and isinstance(signal_info, dict):
                info["signal_quality"] = signal_info.get("signal_strength", "Unknown")
                info["signal_rssi"] = signal_info.get("rssi", "Unknown")
                info["signal_ber"] = signal_info.get("ber", "Unknown")

            success, operator = self.get_network_operator()
            if success:
                info["operator"] = operator

        except Exception as e:
            logger.error(f"Error getting detailed info for {self.port}: {e}")
            info["error"] = str(e)

        return info
