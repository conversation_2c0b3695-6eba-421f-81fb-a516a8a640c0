"""
Refactored modem manager for the SMSHub application.

This module provides the main modem management functionality using
the new modular architecture with proper separation of concerns.
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, Future

try:
    from .config_enhanced import get_enhanced_config
    from .modem_detection import ModemDetector
    from .modem_generic import GenericModem
    from .modem_base import BaseModem, ModemConfig
    from .database_manager import add_message
    from .error_handling import (
        with_error_handling, ErrorCategory, ErrorSeverity,
        get_error_handler, report_error
    )
except ImportError:
    from config_enhanced import get_enhanced_config
    from modem_detection import ModemDetector
    from modem_generic import GenericModem
    from modem_base import BaseModem, ModemConfig
    from database_manager import add_message
    from error_handling import (
        with_error_handling, ErrorCategory, ErrorSeverity,
        get_error_handler, report_error
    )

logger = logging.getLogger(__name__)


class ModemManager:
    """
    Enhanced modem manager with improved architecture.
    
    This class manages multiple modems using a thread pool and
    provides centralized modem operations.
    """

    def __init__(self, max_workers: int = 10):
        """
        Initialize the modem manager.
        
        Args:
            max_workers: Maximum number of worker threads
        """
        self.config = get_enhanced_config()
        self.detector = ModemDetector()
        self.modems: Dict[str, BaseModem] = {}
        self.modem_threads: Dict[str, Dict[str, Any]] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.running = False
        self.scan_interval = self.config.scan_interval
        self._lock = threading.Lock()
        self._scan_thread: Optional[threading.Thread] = None

    def start(self) -> bool:
        """
        Start the modem manager.
        
        Returns:
            True if started successfully, False otherwise
        """
        try:
            with self._lock:
                if self.running:
                    logger.warning("Modem manager is already running")
                    return True

                logger.info("Starting modem manager...")
                
                # Initial modem detection and setup
                self._initialize_modems()
                
                # Start background scanning
                self.running = True
                self._scan_thread = threading.Thread(
                    target=self._scan_loop, 
                    daemon=True, 
                    name="ModemScanner"
                )
                self._scan_thread.start()
                
                logger.info("Modem manager started successfully")
                return True

        except Exception as e:
            logger.error(f"Error starting modem manager: {e}")
            report_error(
                f"Failed to start modem manager: {str(e)}",
                "ModemManager",
                ErrorCategory.CONFIGURATION,
                ErrorSeverity.CRITICAL,
                e
            )
            return False

    def stop(self) -> bool:
        """
        Stop the modem manager and cleanup resources.
        
        Returns:
            True if stopped successfully, False otherwise
        """
        try:
            with self._lock:
                if not self.running:
                    return True

                logger.info("Stopping modem manager...")
                self.running = False

                # Stop all modem threads
                self._stop_all_modem_threads()

                # Disconnect all modems
                for modem in self.modems.values():
                    try:
                        modem.disconnect()
                    except Exception as e:
                        logger.error(f"Error disconnecting modem {modem.port}: {e}")

                # Shutdown thread pool
                self.executor.shutdown(wait=True)

                # Wait for scan thread to finish
                if self._scan_thread and self._scan_thread.is_alive():
                    self._scan_thread.join(timeout=5)

                self.modems.clear()
                self.modem_threads.clear()

                logger.info("Modem manager stopped successfully")
                return True

        except Exception as e:
            logger.error(f"Error stopping modem manager: {e}")
            return False

    @with_error_handling("ModemManager", ErrorCategory.HARDWARE, ErrorSeverity.MEDIUM)
    def _initialize_modems(self) -> None:
        """Initialize detected modems."""
        logger.info("Initializing modems...")
        
        detected_modems = self.detector.detect_modems()
        logger.info(f"Detected {len(detected_modems)} modems")

        for modem_info in detected_modems:
            port = modem_info.get("port")
            if not port:
                continue

            try:
                # Create modem instance
                modem_config = ModemConfig(
                    baudrate=self.config.default_modem_baudrate,
                    timeout=self.config.default_modem_timeout
                )
                
                # Use generic modem for now - can be enhanced to use specific types
                modem = GenericModem(port, modem_config)

                # Store model hint in modem info for fallback
                if hasattr(modem, '_info'):
                    modem._info.model_hint = modem_info.get("model_hint", "Unknown")
                    # Also store the model hint as the initial model if no model is set
                    if not hasattr(modem._info, 'model') or not modem._info.model:
                        modem._info.model = modem_info.get("model_hint", "Unknown")

                # Add a small delay before connecting to avoid rapid connection cycles
                time.sleep(0.2)

                # Try to connect
                if modem.connect():
                    # Initialize for SMS
                    if modem.initialize_for_sms():
                        # Update modem info immediately after successful connection
                        modem.update_info()
                        self.modems[port] = modem
                        logger.info(f"Successfully initialized modem on {port}")

                        # Start SMS monitoring thread
                        self._start_modem_thread(port)
                    else:
                        logger.warning(f"Failed to initialize SMS for modem on {port}")
                        modem.disconnect()
                else:
                    logger.warning(f"Failed to connect to modem on {port}")

            except Exception as e:
                logger.error(f"Error initializing modem on {port}: {e}")

    def _start_modem_thread(self, port: str) -> None:
        """Start SMS monitoring thread for a modem."""
        if port in self.modem_threads:
            return

        stop_event = threading.Event()
        future = self.executor.submit(self._modem_worker, port, stop_event)
        
        self.modem_threads[port] = {
            "stop_event": stop_event,
            "future": future,
            "started_at": time.time()
        }
        
        logger.info(f"Started SMS monitoring thread for modem {port}")

    def _stop_all_modem_threads(self) -> None:
        """Stop all modem monitoring threads."""
        for port, thread_info in self.modem_threads.items():
            try:
                thread_info["stop_event"].set()
                # Give thread some time to finish gracefully
                try:
                    thread_info["future"].result(timeout=5)
                except Exception as e:
                    logger.warning(f"Error stopping thread for {port}: {e}")
            except Exception as e:
                logger.error(f"Error stopping modem thread for {port}: {e}")

    def _modem_worker(self, port: str, stop_event: threading.Event) -> None:
        """Worker thread for monitoring SMS on a specific modem."""
        logger.info(f"Starting SMS worker for modem {port}")
        
        try:
            while not stop_event.is_set():
                try:
                    modem = self.modems.get(port)
                    if not modem or not modem.is_connected:
                        logger.warning(f"Modem {port} not available, stopping worker")
                        break

                    # Check for new SMS messages
                    self._check_sms_for_modem(modem)
                    
                    # Wait before next check
                    stop_event.wait(timeout=10)  # Check every 10 seconds

                except Exception as e:
                    logger.error(f"Error in SMS worker for {port}: {e}")
                    report_error(
                        f"SMS worker error for {port}: {str(e)}",
                        "ModemManager",
                        ErrorCategory.COMMUNICATION,
                        ErrorSeverity.MEDIUM,
                        e,
                        {"port": port}
                    )
                    # Wait a bit before retrying
                    stop_event.wait(timeout=30)

        except Exception as e:
            logger.error(f"Fatal error in SMS worker for {port}: {e}")
        finally:
            logger.info(f"SMS worker for modem {port} stopped")

    def _check_sms_for_modem(self, modem: BaseModem) -> None:
        """
        Check for new SMS messages on a modem.

        NOTE: This method is intentionally disabled because the new SMS system
        uses AT+CNMI notifications instead of polling with AT+CMGL.
        SMS messages are received automatically via the notification system
        and processed by the modem's reader thread.
        """
        # SMS polling disabled - using notification system instead
        # Messages are automatically received via AT+CNMI notifications
        # and processed by the modem's reader thread
        pass

    def _process_sms_message(self, msg_dict: Dict[str, Any], modem_port: str) -> None:
        """Process and store an SMS message."""
        try:
            # Prepare message data for database
            message_data = {
                "modem_port": modem_port,
                "sender": msg_dict.get("sender", "Unknown"),
                "message_body": msg_dict.get("message_body", ""),
                "timestamp_received": msg_dict.get("timestamp"),
                "status_on_modem": msg_dict.get("status", "UNKNOWN"),
                "message_index_on_modem": msg_dict.get("index")
            }

            # Validate essential fields
            if not all(k in message_data and message_data[k] is not None 
                      for k in ["modem_port", "sender", "message_body", "status_on_modem"]):
                logger.warning(f"Skipping SMS due to missing essential data: {msg_dict}")
                return

            # Store in database
            db_id = add_message(message_data)
            if db_id:
                logger.info(f"Stored SMS message {db_id} from {message_data['sender']} on {modem_port}")
            else:
                logger.error(f"Failed to store SMS message from {message_data['sender']} on {modem_port}")

        except Exception as e:
            logger.error(f"Error processing SMS message: {e}")

    def _scan_loop(self) -> None:
        """Background loop for periodic modem scanning."""
        logger.info("Starting modem scan loop")
        
        while self.running:
            try:
                time.sleep(self.scan_interval)
                if not self.running:
                    break
                
                # Perform periodic scan
                self._periodic_scan()
                
            except Exception as e:
                logger.error(f"Error in scan loop: {e}")
                time.sleep(30)  # Wait before retrying

    def _periodic_scan(self) -> None:
        """Perform periodic modem scanning and health checks."""
        try:
            # Check health of existing modems
            disconnected_ports = []
            for port, modem in self.modems.items():
                if not modem.is_connected:
                    disconnected_ports.append(port)
                else:
                    # Update modem info
                    modem.update_info()

            # Remove disconnected modems
            for port in disconnected_ports:
                logger.warning(f"Removing disconnected modem {port}")
                self._remove_modem(port)

            # Scan for new modems
            detected_modems = self.detector.detect_modems()
            for modem_info in detected_modems:
                port = modem_info.get("port")
                if port and port not in self.modems:
                    logger.info(f"Found new modem on {port}")
                    # Try to add the new modem
                    self._add_new_modem(port)

        except Exception as e:
            logger.error(f"Error in periodic scan: {e}")

    def _remove_modem(self, port: str) -> None:
        """Remove a modem and cleanup resources."""
        try:
            # Stop the thread
            if port in self.modem_threads:
                thread_info = self.modem_threads[port]
                thread_info["stop_event"].set()
                del self.modem_threads[port]

            # Disconnect and remove modem
            if port in self.modems:
                modem = self.modems[port]
                modem.disconnect()
                del self.modems[port]

            logger.info(f"Removed modem {port}")

        except Exception as e:
            logger.error(f"Error removing modem {port}: {e}")

    def _add_new_modem(self, port: str) -> None:
        """Add a newly detected modem."""
        try:
            modem_config = ModemConfig(
                baudrate=self.config.default_modem_baudrate,
                timeout=self.config.default_modem_timeout
            )
            
            modem = GenericModem(port, modem_config)

            # Store model hint in modem info for fallback
            if hasattr(modem, '_info'):
                # Try to get model hint from detected modems
                detected_modems = self.detector.detect_modems()
                for detected_modem in detected_modems:
                    if detected_modem.get("port") == port:
                        modem._info.model_hint = detected_modem.get("model_hint", "Unknown")
                        if not hasattr(modem._info, 'model') or not modem._info.model:
                            modem._info.model = detected_modem.get("model_hint", "Unknown")
                        break

            # Add a small delay before connecting to avoid rapid connection cycles
            time.sleep(0.2)

            if modem.connect() and modem.initialize_for_sms():
                # Update modem info immediately after successful connection
                modem.update_info()
                self.modems[port] = modem
                self._start_modem_thread(port)
                logger.info(f"Successfully added new modem on {port}")
            else:
                modem.disconnect()
                logger.warning(f"Failed to initialize new modem on {port}")

        except Exception as e:
            logger.error(f"Error adding new modem on {port}: {e}")

    def get_modem_status(self) -> List[Dict[str, Any]]:
        """
        Get status of all managed modems.

        Returns:
            List of modem status dictionaries
        """
        status_list = []

        try:
            # Get active modems
            for port, modem in self.modems.items():
                try:
                    info = modem.get_detailed_info()

                    # Format for API compatibility
                    status_dict = {
                        "id": port,
                        "name": f"Modem {port}",
                        "port": port,
                        "status": "RESPONSIVE" if modem.is_connected else "DISCONNECTED",
                        "signal_strength": info.get("signal_quality", "N/A"),
                        "operator": info.get("operator", "N/A"),
                        "sim_status": info.get("sim_status", "N/A"),
                        "imei": info.get("imei", "N/A"),
                        "phone_number": info.get("phone_number", "N/A"),
                        "model": info.get("model", "N/A"),
                        "last_activity": "Active" if modem.is_connected else "N/A",
                        "recent_sms": [],
                        "sms_mode_status": "Text" if modem.is_connected else "N/A",
                        "cnmi_status": "OK" if modem.is_connected else "N/A"
                    }

                    status_list.append(status_dict)

                except Exception as e:
                    logger.error(f"Error getting status for modem {port}: {e}")

            # If no active modems, show detected modems with hints
            if not status_list:
                logger.info("No active modems found, showing detected modems with model hints")
                try:
                    # Get all USB ports with modem VID/PID combinations
                    usb_ports = self.detector.scan_usb_ports()
                    detected_count = 0

                    for port_info in usb_ports:
                        if detected_count >= 5:  # Limit to 5 modems
                            break

                        port_device = port_info.get("device", "Unknown")
                        vid = port_info.get("vid")
                        pid = port_info.get("pid")

                        if vid is None or pid is None:
                            continue

                        # Check if this is a known modem type
                        modem_config = self.detector.identify_modem_type(vid, pid)
                        if not modem_config:
                            continue

                        model_hint = modem_config.get("model_hint", "Unknown Modem")

                        status_dict = {
                            "id": port_device,
                            "name": f"Modem {port_device}",
                            "port": port_device,
                            "status": "DETECTED_NOT_ACTIVE",
                            "signal_strength": "N/A",
                            "operator": "N/A",
                            "sim_status": "N/A",
                            "imei": "N/A",
                            "phone_number": "N/A",
                            "model": model_hint,  # Use the detection hint as model
                            "last_activity": "Detected but not responsive",
                            "recent_sms": [],
                            "sms_mode_status": "N/A",
                            "cnmi_status": "N/A"
                        }

                        status_list.append(status_dict)
                        detected_count += 1

                except Exception as e:
                    logger.error(f"Error getting detected modems: {e}")

        except Exception as e:
            logger.error(f"Error getting modem status: {e}")

        return status_list

    def send_sms(self, port: str, phone_number: str, message: str) -> bool:
        """
        Send an SMS message via a specific modem.
        
        Args:
            port: Modem port to use
            phone_number: Destination phone number
            message: Message text
            
        Returns:
            True if successful, False otherwise
        """
        try:
            modem = self.modems.get(port)
            if not modem:
                logger.error(f"Modem {port} not found")
                return False
            
            if not modem.is_connected:
                logger.error(f"Modem {port} not connected")
                return False
            
            return modem.send_sms(phone_number, message)
            
        except Exception as e:
            logger.error(f"Error sending SMS via {port}: {e}")
            return False


# Global modem manager instance
_modem_manager: Optional[ModemManager] = None


def get_modem_manager() -> ModemManager:
    """Get the global modem manager instance."""
    global _modem_manager
    if _modem_manager is None:
        _modem_manager = ModemManager()
    return _modem_manager


def initialize_all_modems() -> None:
    """Initialize all modems using the new manager."""
    manager = get_modem_manager()
    manager.start()


def shutdown_all_modem_readers() -> None:
    """Shutdown all modem operations."""
    global _modem_manager
    if _modem_manager:
        _modem_manager.stop()


def get_modem_status() -> List[Dict[str, Any]]:
    """Get status of all modems."""
    manager = get_modem_manager()
    return manager.get_modem_status()


def fetch_and_store_new_sms_from_all_modems() -> None:
    """Fetch and store new SMS from all modems (compatibility function)."""
    # This is handled automatically by the new manager
    logger.info("SMS fetching is handled automatically by the new modem manager")
