"""
Enhanced configuration management for the SMSHub application.

This module provides improved configuration handling with validation,
environment variable support, and type safety.
"""

import json
import logging
import os
import threading
import time
from typing import Dict, Any, Optional, Union, List, Callable
from dataclasses import dataclass, field
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logger = logging.getLogger(__name__)


@dataclass
class ModemConfig:
    """Configuration for individual modems."""
    id: str
    port: str
    phone_number: Optional[str] = None
    enabled: bool = True
    baudrate: int = 115200
    timeout: float = 1.0
    max_retries: int = 3
    retry_delay: float = 1.0


@dataclass
class ServerConfig:
    """Server configuration settings."""
    host: str = "0.0.0.0"
    port: int = 5000
    debug: bool = False
    use_ssl: bool = False
    ssl_cert_path: Optional[str] = None
    ssl_key_path: Optional[str] = None


@dataclass
class TunnelConfig:
    """Tunnel configuration for external access."""
    enabled: bool = False
    type: str = "localtonet"
    name: str = "smshub-agent"
    auth_token: Optional[str] = None


@dataclass
class SMSHubConfig:
    """SMSHub service configuration."""
    api_key: str = ""
    agent_id: str = ""
    server_url: str = ""
    protocol_key: str = ""
    service_country_name: str = "usaphysic"
    service_operator_name: str = "any"


@dataclass
class ForwardingConfig:
    """SMS forwarding configuration."""
    enabled: bool = False
    url: Optional[str] = None
    api_key: Optional[str] = None
    retry_delay: int = 10
    max_attempts: int = 5
    service_interval: int = 60


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5


@dataclass
class AppConfig:
    """Main application configuration."""
    server: ServerConfig = field(default_factory=ServerConfig)
    tunnel: TunnelConfig = field(default_factory=TunnelConfig)
    smshub: SMSHubConfig = field(default_factory=SMSHubConfig)
    forwarding: ForwardingConfig = field(default_factory=ForwardingConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    modems: List[ModemConfig] = field(default_factory=list)
    services: Dict[str, bool] = field(default_factory=dict)
    
    # General settings
    auto_start_server: bool = True
    scan_interval: int = 30
    default_modem_baudrate: int = 115200
    default_modem_timeout: float = 1.0


class ConfigFileHandler(FileSystemEventHandler):
    """File system event handler for configuration file changes."""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.last_modified = 0

    def on_modified(self, event):
        """Handle file modification events."""
        if event.is_directory:
            return

        if Path(event.src_path) == self.config_manager.config_path:
            # Debounce rapid file changes
            current_time = time.time()
            if current_time - self.last_modified > 1.0:  # 1 second debounce
                self.last_modified = current_time
                self.config_manager._on_config_file_changed()


class ConfigManager:
    """Enhanced configuration manager with validation, environment support, and hot reload."""

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.

        Args:
            config_path: Path to configuration file, auto-detected if None
        """
        self.config_path = self._find_config_path(config_path)
        self._config: Optional[AppConfig] = None
        self._raw_config: Dict[str, Any] = {}
        self._reload_callbacks: List[Callable[[AppConfig], None]] = []
        self._file_observer: Optional[Observer] = None
        self._hot_reload_enabled = False
        self._lock = threading.RLock()
    
    def _find_config_path(self, config_path: Optional[str]) -> Path:
        """Find the configuration file path."""
        if config_path:
            return Path(config_path)
        
        # Try different locations
        current_dir = Path(__file__).parent
        possible_paths = [
            current_dir / ".." / "config.json",
            current_dir / "config.json",
            Path.cwd() / "config.json",
            Path.cwd() / "smshub_app" / "config.json"
        ]
        
        for path in possible_paths:
            if path.exists():
                return path.resolve()
        
        # Default to the first location
        return possible_paths[0].resolve()
    
    def load(self) -> AppConfig:
        """
        Load configuration from file and environment variables.
        
        Returns:
            Loaded and validated configuration
        """
        try:
            # Load from file
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._raw_config = json.load(f)
                logger.info(f"Configuration loaded from {self.config_path}")
            else:
                logger.warning(f"Configuration file not found at {self.config_path}, using defaults")
                self._raw_config = {}
            
            # Apply environment variable overrides
            self._apply_env_overrides()
            
            # Parse and validate configuration
            self._config = self._parse_config()
            
            # Validate configuration
            self._validate_config()
            
            return self._config
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            # Return default configuration on error
            self._config = AppConfig()
            return self._config
    
    def _apply_env_overrides(self):
        """Apply environment variable overrides to configuration."""
        env_mappings = {
            "SMSHUB_API_KEY": ["smshub_api_key"],
            "SMSHUB_AGENT_ID": ["smshub_agent_id"],
            "SMSHUB_SERVER_URL": ["smshub_server_url"],
            "SMSHUB_PROTOCOL_KEY": ["smshub_protocol_key"],
            "SERVER_HOST": ["app_host"],
            "SERVER_PORT": ["app_port"],
            "DEBUG_MODE": ["debug_mode"],
            "LOG_LEVEL": ["log_level"],
            "SCAN_INTERVAL": ["scan_interval"],
            "AUTO_START_SERVER": ["auto_start_server"],
        }
        
        for env_var, config_keys in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Convert value to appropriate type
                if env_var in ["SERVER_PORT", "SCAN_INTERVAL"]:
                    try:
                        value = int(value)
                    except ValueError:
                        logger.warning(f"Invalid integer value for {env_var}: {value}")
                        continue
                elif env_var in ["DEBUG_MODE", "AUTO_START_SERVER"]:
                    value = value.lower() in ("true", "1", "yes", "on")
                
                # Set the value in raw config
                for key in config_keys:
                    self._raw_config[key] = value
                    logger.info(f"Applied environment override: {env_var} -> {key} = {value}")
    
    def _parse_config(self) -> AppConfig:
        """Parse raw configuration into structured config object."""
        config = AppConfig()
        
        # Server configuration
        config.server.host = self._raw_config.get("app_host", config.server.host)
        config.server.port = self._raw_config.get("app_port", self._raw_config.get("server_port", config.server.port))
        config.server.debug = self._raw_config.get("debug_mode", config.server.debug)
        config.server.use_ssl = self._raw_config.get("use_ssl", config.server.use_ssl)
        
        # Tunnel configuration
        tunnel_config = self._raw_config.get("tunnel", {})
        config.tunnel.enabled = tunnel_config.get("enabled", config.tunnel.enabled)
        config.tunnel.type = tunnel_config.get("type", config.tunnel.type)
        config.tunnel.name = tunnel_config.get("name", config.tunnel.name)
        
        # SMSHub configuration
        config.smshub.api_key = self._raw_config.get("smshub_api_key", config.smshub.api_key)
        config.smshub.agent_id = self._raw_config.get("smshub_agent_id", config.smshub.agent_id)
        config.smshub.server_url = self._raw_config.get("smshub_server_url", config.smshub.server_url)
        config.smshub.protocol_key = self._raw_config.get("smshub_protocol_key", config.smshub.protocol_key)
        config.smshub.service_country_name = self._raw_config.get("service_country_name", config.smshub.service_country_name)
        config.smshub.service_operator_name = self._raw_config.get("service_operator_name", config.smshub.service_operator_name)
        
        # Forwarding configuration
        config.forwarding.enabled = self._raw_config.get("sms_forwarding_enabled", config.forwarding.enabled)
        config.forwarding.url = self._raw_config.get("sms_forwarding_url", config.forwarding.url)
        config.forwarding.api_key = self._raw_config.get("agent_api_key", config.forwarding.api_key)
        config.forwarding.retry_delay = self._raw_config.get("sms_forwarding_retry_delay_seconds", config.forwarding.retry_delay)
        config.forwarding.max_attempts = self._raw_config.get("sms_forwarding_max_attempts", config.forwarding.max_attempts)
        config.forwarding.service_interval = self._raw_config.get("sms_forwarding_service_interval_seconds", config.forwarding.service_interval)
        
        # Logging configuration
        config.logging.level = self._raw_config.get("log_level", config.logging.level)
        
        # General settings
        config.auto_start_server = self._raw_config.get("auto_start_server", config.auto_start_server)
        config.scan_interval = self._raw_config.get("scan_interval", config.scan_interval)
        config.default_modem_baudrate = self._raw_config.get("default_modem_baudrate", config.default_modem_baudrate)
        config.default_modem_timeout = self._raw_config.get("default_modem_timeout", config.default_modem_timeout)
        
        # Services
        config.services = self._raw_config.get("services", {})
        
        # Modems
        modems_data = self._raw_config.get("modems", [])
        config.modems = [self._parse_modem_config(modem_data) for modem_data in modems_data]
        
        return config
    
    def _parse_modem_config(self, modem_data: Dict[str, Any]) -> ModemConfig:
        """Parse individual modem configuration."""
        return ModemConfig(
            id=modem_data.get("id", ""),
            port=modem_data.get("port", ""),
            phone_number=modem_data.get("phone_number"),
            enabled=modem_data.get("enabled", True),
            baudrate=modem_data.get("baudrate", 115200),
            timeout=modem_data.get("timeout", 1.0),
            max_retries=modem_data.get("max_retries", 3),
            retry_delay=modem_data.get("retry_delay", 1.0)
        )
    
    def _validate_config(self):
        """Validate the loaded configuration."""
        if not self._config:
            raise ValueError("Configuration not loaded")
        
        # Validate required SMSHub settings
        if not self._config.smshub.protocol_key:
            logger.warning("SMSHub protocol key not configured - some features may not work")
        
        # Validate server port
        if not (1 <= self._config.server.port <= 65535):
            raise ValueError(f"Invalid server port: {self._config.server.port}")
        
        # Validate logging level
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self._config.logging.level.upper() not in valid_levels:
            logger.warning(f"Invalid log level '{self._config.logging.level}', using INFO")
            self._config.logging.level = "INFO"
        
        logger.info("Configuration validation completed successfully")
    
    def save(self, config: Optional[AppConfig] = None):
        """
        Save configuration to file.
        
        Args:
            config: Configuration to save, uses current config if None
        """
        if config:
            self._config = config
        
        if not self._config:
            raise ValueError("No configuration to save")
        
        try:
            # Convert config back to dictionary format
            config_dict = self._config_to_dict(self._config)
            
            # Ensure directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write to file
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            
            logger.info(f"Configuration saved to {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            raise
    
    def _config_to_dict(self, config: AppConfig) -> Dict[str, Any]:
        """Convert AppConfig back to dictionary format."""
        # This is a simplified conversion - could be enhanced
        return self._raw_config

    def enable_hot_reload(self) -> bool:
        """
        Enable hot reloading of configuration file.

        Returns:
            True if hot reload was enabled successfully
        """
        try:
            if self._hot_reload_enabled:
                return True

            if not self.config_path.exists():
                logger.warning(f"Config file {self.config_path} does not exist, cannot enable hot reload")
                return False

            # Set up file watcher
            self._file_observer = Observer()
            event_handler = ConfigFileHandler(self)
            self._file_observer.schedule(
                event_handler,
                str(self.config_path.parent),
                recursive=False
            )
            self._file_observer.start()
            self._hot_reload_enabled = True

            logger.info(f"Hot reload enabled for config file: {self.config_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to enable hot reload: {e}")
            return False

    def disable_hot_reload(self) -> None:
        """Disable hot reloading of configuration file."""
        if self._file_observer:
            self._file_observer.stop()
            self._file_observer.join()
            self._file_observer = None
        self._hot_reload_enabled = False
        logger.info("Hot reload disabled")

    def add_reload_callback(self, callback: Callable[[AppConfig], None]) -> None:
        """
        Add a callback to be called when configuration is reloaded.

        Args:
            callback: Function to call with new configuration
        """
        self._reload_callbacks.append(callback)

    def remove_reload_callback(self, callback: Callable[[AppConfig], None]) -> None:
        """
        Remove a reload callback.

        Args:
            callback: Function to remove
        """
        if callback in self._reload_callbacks:
            self._reload_callbacks.remove(callback)

    def _on_config_file_changed(self) -> None:
        """Handle configuration file changes."""
        try:
            logger.info("Configuration file changed, reloading...")

            with self._lock:
                old_config = self._config
                new_config = self.load()

                # Call reload callbacks
                for callback in self._reload_callbacks:
                    try:
                        callback(new_config)
                    except Exception as e:
                        logger.error(f"Error in config reload callback: {e}")

                logger.info("Configuration reloaded successfully")

        except Exception as e:
            logger.error(f"Error reloading configuration: {e}")

    def reload(self) -> AppConfig:
        """
        Manually reload configuration from file.

        Returns:
            Reloaded configuration
        """
        with self._lock:
            return self.load()

    @property
    def config(self) -> Optional[AppConfig]:
        """Get the current configuration."""
        return self._config

    @property
    def hot_reload_enabled(self) -> bool:
        """Check if hot reload is enabled."""
        return self._hot_reload_enabled


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def load_config() -> Dict[str, Any]:
    """
    Load configuration using the enhanced config manager.
    
    This function maintains compatibility with the existing codebase
    while providing enhanced configuration management.
    
    Returns:
        Configuration dictionary
    """
    try:
        manager = get_config_manager()
        config = manager.load()
        
        # Convert back to dictionary format for compatibility
        return manager._raw_config
        
    except Exception as e:
        logger.error(f"Error in load_config: {e}")
        return {}


def get_enhanced_config() -> AppConfig:
    """
    Get the enhanced configuration object.
    
    Returns:
        Structured configuration object
    """
    manager = get_config_manager()
    if manager.config is None:
        manager.load()
    return manager.config
