"""
Tests for Phase 3 performance optimization enhancements.

This module contains tests for the new performance features including
caching, database optimization, API performance, and resource optimization.
"""

import pytest
import sys
import os
import time
import tempfile
from unittest.mock import Mock, patch, MagicMock

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import Phase 3 modules
from smshub_app.app.caching_manager import CachingManager, InMemoryCache, CacheConfig
from smshub_app.app.database_optimizer import QueryOptimizer, DatabaseMaintenance
from smshub_app.app.api_performance import ResponseCompressor, APICacheManager, PaginationHelper
from smshub_app.app.resource_optimizer import MemoryOptimizer, CPUOptimizer, ResourceMonitor


class TestCachingManager:
    """Test the caching management system."""
    
    def test_cache_config_creation(self):
        """Test CacheConfig creation."""
        config = CacheConfig()
        assert config.enabled is True
        assert config.redis_host == "localhost"
        assert config.redis_port == 6379
        assert config.default_ttl == 300
    
    def test_in_memory_cache(self):
        """Test InMemoryCache functionality."""
        cache = InMemoryCache(default_ttl=60)
        
        # Test set and get
        result = cache.set("test_key", "test_value")
        assert result is True
        
        value = cache.get("test_key")
        assert value == "test_value"
        
        # Test non-existent key
        value = cache.get("non_existent")
        assert value is None
        
        # Test delete
        result = cache.delete("test_key")
        assert result is True
        
        value = cache.get("test_key")
        assert value is None
    
    def test_in_memory_cache_ttl(self):
        """Test TTL functionality in InMemoryCache."""
        cache = InMemoryCache(default_ttl=1)  # 1 second TTL
        
        # Set value with short TTL
        cache.set("ttl_key", "ttl_value", ttl=1)
        
        # Should be available immediately
        value = cache.get("ttl_key")
        assert value == "ttl_value"
        
        # Wait for expiration (in real test, you might mock time)
        time.sleep(1.1)
        
        # Should be expired
        value = cache.get("ttl_key")
        assert value is None
    
    def test_in_memory_cache_stats(self):
        """Test cache statistics."""
        cache = InMemoryCache()
        
        # Add some data
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        
        stats = cache.get_stats()
        assert stats["type"] == "in_memory"
        assert stats["total_keys"] >= 2
    
    @patch('smshub_app.app.caching_manager.REDIS_AVAILABLE', False)
    def test_caching_manager_fallback(self):
        """Test CachingManager fallback to in-memory cache."""
        config = CacheConfig(enabled=True)
        manager = CachingManager(config)
        
        # Should initialize successfully
        result = manager._initialize()
        assert result is True
        
        # Should use in-memory cache
        assert isinstance(manager._cache, InMemoryCache)
    
    def test_caching_manager_disabled(self):
        """Test CachingManager when caching is disabled."""
        config = CacheConfig(enabled=False)
        manager = CachingManager(config)
        
        result = manager._initialize()
        assert result is False
        
        # Operations should return None/False gracefully
        assert manager.get("test") is None
        assert manager.set("test", "value") is False
    
    def test_specialized_caching_methods(self):
        """Test specialized caching methods."""
        config = CacheConfig(enabled=True)
        manager = CachingManager(config)
        manager._initialize()
        
        # Test SMS message caching
        messages = [{"id": 1, "text": "test"}]
        result = manager.cache_sms_messages(messages, "recent")
        assert result is True
        
        cached_messages = manager.get_cached_sms_messages("recent")
        assert cached_messages == messages
        
        # Test modem status caching
        status = {"port": "COM1", "status": "connected"}
        result = manager.cache_modem_status("COM1", status)
        assert result is True
        
        cached_status = manager.get_cached_modem_status("COM1")
        assert cached_status == status


class TestDatabaseOptimizer:
    """Test the database optimization system."""
    
    @patch('smshub_app.app.database_optimizer.get_database_pool')
    @patch('smshub_app.app.database_optimizer.get_caching_manager')
    def test_query_optimizer_creation(self, mock_cache, mock_pool):
        """Test QueryOptimizer creation."""
        mock_pool.return_value = Mock()
        mock_cache.return_value = Mock()
        
        optimizer = QueryOptimizer()
        assert optimizer.pool is not None
        assert optimizer.cache is not None
        assert optimizer.query_stats == []
    
    @patch('smshub_app.app.database_optimizer.get_database_pool')
    @patch('smshub_app.app.database_optimizer.get_caching_manager')
    def test_optimized_message_query(self, mock_cache, mock_pool):
        """Test optimized message query."""
        # Mock database connection
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.fetchall.return_value = [{"id": 1, "text": "test"}]
        mock_conn.cursor.return_value = mock_cursor
        mock_pool.return_value.get_connection.return_value.__enter__.return_value = mock_conn
        
        # Mock cache miss
        mock_cache.return_value.get.return_value = None
        mock_cache.return_value.set.return_value = True
        
        optimizer = QueryOptimizer()
        messages = optimizer.get_messages_optimized(limit=10, offset=0)
        
        assert len(messages) == 1
        mock_cursor.execute.assert_called()
        mock_cache.return_value.set.assert_called()
    
    @patch('smshub_app.app.database_optimizer.get_database_pool')
    def test_database_maintenance(self, mock_pool):
        """Test DatabaseMaintenance functionality."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_pool.return_value.get_connection.return_value.__enter__.return_value = mock_conn
        
        maintenance = DatabaseMaintenance()
        
        # Test vacuum
        result = maintenance.vacuum_database()
        assert result is True
        mock_cursor.execute.assert_called()
        
        # Test cleanup
        mock_cursor.rowcount = 5
        deleted_count = maintenance.cleanup_old_messages(days_old=30)
        assert deleted_count == 5
    
    def test_query_stats_recording(self):
        """Test query statistics recording."""
        optimizer = QueryOptimizer()
        
        # Record some stats
        optimizer._record_stats("test_query", 0.5, 10, cache_hit=False)
        optimizer._record_stats("test_query", 0.3, 5, cache_hit=True)
        
        assert len(optimizer.query_stats) == 2
        
        # Get performance stats
        stats = optimizer.get_performance_stats()
        assert stats["total_queries"] == 2
        assert "test_query" in stats["queries_by_type"]


class TestAPIPerformance:
    """Test the API performance optimization system."""
    
    def test_response_compressor(self):
        """Test ResponseCompressor functionality."""
        # Mock Flask request and response
        with patch('smshub_app.app.api_performance.request') as mock_request:
            mock_request.headers.get.return_value = "gzip, deflate"
            
            # Mock response
            mock_response = Mock()
            mock_response.headers = {}
            mock_response.get_data.return_value = b"test data" * 200  # Make it large enough
            mock_response.content_length = 1800
            mock_response.headers.get.return_value = "application/json"
            
            # Test should_compress
            should_compress = ResponseCompressor.should_compress(mock_response)
            assert should_compress is True
    
    def test_api_cache_manager(self):
        """Test APICacheManager functionality."""
        manager = APICacheManager()
        
        # Test cache key generation
        key = manager.generate_cache_key("test_endpoint", param1="value1", param2="value2")
        assert "api:test_endpoint:" in key
        assert "param1=value1" in key
        assert "param2=value2" in key
    
    def test_pagination_helper(self):
        """Test PaginationHelper functionality."""
        # Mock Flask request
        with patch('smshub_app.app.api_performance.request') as mock_request:
            mock_request.args.get.side_effect = lambda key, default: {
                'limit': '25',
                'page': '2'
            }.get(key, default)
            
            limit, offset = PaginationHelper.get_pagination_params()
            assert limit == 25
            assert offset == 25  # (page 2 - 1) * 25
        
        # Test paginated response creation
        data = [{"id": i} for i in range(10)]
        response = PaginationHelper.create_paginated_response(data, 100, 10, 20)
        
        assert response["data"] == data
        assert response["pagination"]["current_page"] == 3
        assert response["pagination"]["total_pages"] == 10
        assert response["pagination"]["total_items"] == 100


class TestResourceOptimizer:
    """Test the resource optimization system."""
    
    @patch('smshub_app.app.resource_optimizer.psutil')
    def test_memory_optimizer(self, mock_psutil):
        """Test MemoryOptimizer functionality."""
        # Mock psutil
        mock_process = Mock()
        mock_process.memory_info.return_value = Mock(rss=100*1024*1024, vms=200*1024*1024)
        mock_process.memory_percent.return_value = 15.5
        mock_process.num_threads.return_value = 10
        mock_process.open_files.return_value = []
        mock_psutil.Process.return_value = mock_process
        
        mock_psutil.virtual_memory.return_value = Mock(
            total=8*1024*1024*1024,
            available=4*1024*1024*1024,
            used=4*1024*1024*1024,
            percent=50.0
        )
        
        optimizer = MemoryOptimizer()
        memory_usage = optimizer.get_memory_usage()
        
        assert "process" in memory_usage
        assert "system" in memory_usage
        assert memory_usage["process"]["rss_mb"] == 100.0
        assert memory_usage["process"]["percent"] == 15.5
    
    @patch('smshub_app.app.resource_optimizer.psutil')
    def test_cpu_optimizer(self, mock_psutil):
        """Test CPUOptimizer functionality."""
        # Mock psutil
        mock_process = Mock()
        mock_process.cpu_percent.return_value = 25.5
        mock_process.num_threads.return_value = 8
        mock_process.cpu_times.return_value = Mock(_asdict=lambda: {"user": 10.0, "system": 5.0})
        mock_psutil.Process.return_value = mock_process
        
        mock_psutil.cpu_percent.return_value = 45.0
        mock_psutil.cpu_count.return_value = 4
        
        optimizer = CPUOptimizer()
        cpu_usage = optimizer.get_cpu_usage()
        
        assert "process" in cpu_usage
        assert "system" in cpu_usage
        assert cpu_usage["process"]["cpu_percent"] == 25.5
        assert cpu_usage["system"]["cpu_count"] == 4
    
    @patch('smshub_app.app.resource_optimizer.psutil')
    def test_resource_monitor(self, mock_psutil):
        """Test ResourceMonitor functionality."""
        # Mock psutil
        mock_process = Mock()
        mock_process.memory_info.return_value = Mock(rss=100*1024*1024)
        mock_process.memory_percent.return_value = 15.0
        mock_process.cpu_percent.return_value = 20.0
        mock_process.num_threads.return_value = 5
        mock_process.open_files.return_value = []
        mock_psutil.Process.return_value = mock_process
        
        mock_psutil.disk_usage.return_value = Mock(
            total=1000*1024*1024*1024,
            used=500*1024*1024*1024
        )
        
        monitor = ResourceMonitor()
        
        # Test metrics collection
        metrics = monitor._collect_metrics()
        assert metrics is not None
        assert metrics.memory_percent == 15.0
        assert metrics.cpu_percent == 20.0
        assert metrics.thread_count == 5
    
    def test_threshold_checking(self):
        """Test threshold checking functionality."""
        monitor = ResourceMonitor()
        
        # Mock metrics that exceed thresholds
        from smshub_app.app.resource_optimizer import ResourceMetrics
        high_memory_metrics = ResourceMetrics(
            timestamp=time.time(),
            cpu_percent=50.0,
            memory_percent=85.0,  # Above default threshold of 80%
            memory_mb=1000.0,
            disk_usage_percent=70.0,
            thread_count=10,
            open_files=50
        )
        
        # Add callback to track threshold violations
        violations = []
        def callback(threshold_type, metrics):
            violations.append(threshold_type)
        
        monitor.add_optimization_callback(callback)
        monitor._check_thresholds(high_memory_metrics)
        
        assert "memory_threshold" in violations


class TestIntegrationPhase3:
    """Integration tests for Phase 3 performance enhancements."""
    
    def test_caching_with_database_optimization(self):
        """Test integration between caching and database optimization."""
        # This would test that database queries use caching appropriately
        pass
    
    def test_api_performance_with_caching(self):
        """Test API performance enhancements with caching."""
        # This would test that API endpoints use caching correctly
        pass
    
    def test_resource_monitoring_with_optimization(self):
        """Test resource monitoring triggering optimization."""
        # This would test the full optimization pipeline
        pass


def test_phase3_module_imports():
    """Test that all Phase 3 modules can be imported successfully."""
    try:
        import smshub_app.app.caching_manager
        import smshub_app.app.database_optimizer
        import smshub_app.app.api_performance
        import smshub_app.app.resource_optimizer
        
        # Test that key classes can be instantiated
        from smshub_app.app.caching_manager import CachingManager, CacheConfig
        from smshub_app.app.database_optimizer import QueryOptimizer, DatabaseMaintenance
        from smshub_app.app.api_performance import APICacheManager, PaginationHelper
        from smshub_app.app.resource_optimizer import MemoryOptimizer, CPUOptimizer
        
        # These should not raise exceptions
        config = CacheConfig()
        cache_manager = CachingManager(config)
        api_cache = APICacheManager()
        pagination = PaginationHelper()
        memory_opt = MemoryOptimizer()
        cpu_opt = CPUOptimizer()
        
        assert config is not None
        assert cache_manager is not None
        assert api_cache is not None
        assert pagination is not None
        assert memory_opt is not None
        assert cpu_opt is not None
        
    except ImportError as e:
        pytest.fail(f"Phase 3 module import test failed: {e}")


if __name__ == "__main__":
    # Run tests when script is executed directly
    pytest.main([__file__, "-v"])
