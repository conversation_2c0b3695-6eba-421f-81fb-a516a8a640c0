"""
Modem detection module for the SMSHub application.

This module handles automatic detection of connected modems
by scanning USB ports and identifying supported modem types.
"""

import logging
import time
from typing import List, Dict, Any, Optional

try:
    import serial
    import serial.tools.list_ports
except ImportError:
    serial = None

try:
    from .config_manager import load_config
except ImportError:
    from config_manager import load_config

try:
    from .modem_communication import ModemCommunicator, check_modem_connection
except ImportError:
    from modem_communication import ModemCommunicator, check_modem_connection

logger = logging.getLogger(__name__)


# Known modem configurations
KNOWN_MODEMS = [
    {
        "vid": 0x8087, 
        "pid": 0x095A, 
        "model_hint": "Fibocom L850-GL (Intel)",
        "class_name": "FibocomL850GL"
    },
    {
        "vid": 0x413C, 
        "pid": 0x81D9, 
        "model_hint": "Fibocom L850-GL (Dell)",
        "class_name": "FibocomL850GL"
    },
    {
        "vid": 0x1410, 
        "pid": 0xB001, 
        "model_hint": "Novatel USB551L",
        "class_name": "NovatelUSB551L"
    }
]


class ModemDetector:
    """
    Handles detection and identification of connected modems.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the modem detector.
        
        Args:
            config: Configuration dictionary, loads from file if None
        """
        self.config = config or load_config()
        self.default_baudrate = self.config.get("default_modem_baudrate", 115200)
        self.default_timeout = float(self.config.get("default_modem_timeout", 1.0))

    def scan_usb_ports(self) -> List[Dict[str, Any]]:
        """
        Scan USB ports for connected devices.
        
        Returns:
            List of port information dictionaries
        """
        if serial is None or not hasattr(serial, 'tools') or not hasattr(serial.tools, 'list_ports'):
            logger.warning("pyserial or serial.tools.list_ports not found. Cannot auto-detect modems.")
            return []

        ports = []
        try:
            for port_info in serial.tools.list_ports.comports():
                port_data = {
                    "device": port_info.device,
                    "description": port_info.description,
                    "hwid": port_info.hwid,
                    "vid": port_info.vid,
                    "pid": port_info.pid,
                    "serial_number": port_info.serial_number,
                    "manufacturer": port_info.manufacturer,
                    "product": port_info.product
                }
                ports.append(port_data)
                logger.debug(f"Found port: {port_data}")
        except Exception as e:
            logger.error(f"Error scanning USB ports: {e}")

        return ports

    def identify_modem_type(self, vid: int, pid: int) -> Optional[Dict[str, Any]]:
        """
        Identify modem type based on VID/PID.
        
        Args:
            vid: Vendor ID
            pid: Product ID
            
        Returns:
            Modem configuration dictionary or None if unknown
        """
        for modem_config in KNOWN_MODEMS:
            if modem_config["vid"] == vid and modem_config["pid"] == pid:
                return modem_config.copy()
        return None

    def probe_modem_port(self, port_device: str, model_hint: str = "Unknown") -> Optional[Dict[str, Any]]:
        """
        Probe a specific port to check if it contains a responsive modem.
        Uses a single connection to minimize connect/disconnect cycles.

        Args:
            port_device: Port device path
            model_hint: Hint about the expected modem model

        Returns:
            Modem information dictionary or None if not responsive
        """
        log_prefix = f"Port '{port_device}' ({model_hint}):"
        temp_comm = None

        try:
            logger.debug(f"{log_prefix} Creating ModemCommunicator instance for probe.")
            temp_comm = ModemCommunicator(
                port=port_device,
                baudrate=self.default_baudrate,
                timeout=0.5
            )

            if not temp_comm.connect():
                logger.warning(f"{log_prefix} Failed to connect for initial probe.")
                return None

            # Test basic responsiveness
            success_at, _ = temp_comm.send_at_command("AT", timeout_override=1.0)
            success_ati = False

            if not success_at:
                success_ati, _ = temp_comm.send_at_command("ATI", timeout_override=1.5)

            is_responsive = success_at or success_ati

            if is_responsive:
                logger.info(f"{log_prefix} Responded to AT/ATI. Getting detailed info in same connection...")
                # Get detailed info using the same connection to avoid reconnection
                detailed_info = self._get_detailed_info_with_connection(temp_comm, port_device, model_hint)
                temp_comm.disconnect()
                return detailed_info
            else:
                logger.warning(f"{log_prefix} Did not respond to AT commands.")
                temp_comm.disconnect()
                return None

        except Exception as e:
            logger.error(f"{log_prefix} Error during probe: {e}")
            return None
        finally:
            if temp_comm and temp_comm.is_connected:
                temp_comm.disconnect()

    def _get_detailed_info_with_connection(self, communicator: 'ModemCommunicator', port_device: str, model_hint: str = "Unknown") -> Dict[str, Any]:
        """
        Get detailed information from a modem using an existing connection.
        This avoids the need to reconnect, reducing connection cycles.

        Args:
            communicator: Already connected ModemCommunicator instance
            port_device: Port device path
            model_hint: Hint about the modem model

        Returns:
            Dictionary containing detailed modem information
        """
        try:
            # Create a temporary GenericModem instance with the existing communicator
            from .modem_generic import GenericModem, ModemConfig

            config = ModemConfig(
                baudrate=self.default_baudrate,
                timeout=self.default_timeout
            )

            # Create modem instance but don't connect (use existing connection)
            modem = GenericModem(port_device, config)
            modem.communicator = communicator  # Use existing connection
            modem._is_connected = True  # Mark as connected

            # Get detailed info without connecting
            detailed_info = modem.get_detailed_info()

            # Don't disconnect here - let the caller handle it
            modem.communicator = None  # Prevent modem from disconnecting
            modem._is_connected = False

            # Add model hint if not already present
            if detailed_info.get("model") in ["N/A", "UNKNOWN", "", "Unknown"]:
                detailed_info["modem_name_config"] = model_hint
                detailed_info["model"] = model_hint  # Use model hint as model name

            # Always store the model hint for fallback
            detailed_info["model_hint"] = model_hint

            logger.info(f"Detailed info for {port_device}: {detailed_info}")
            return detailed_info

        except Exception as e:
            logger.error(f"Error getting detailed info for {port_device}: {e}")
            return {
                "port": port_device,
                "status": "ERROR",
                "connection_details": f"Error during info gathering: {e}",
                "modem_name_config": model_hint,
                "model_hint": model_hint
            }

    def get_detailed_modem_info(self, port_device: str, model_hint: str = "Unknown") -> Dict[str, Any]:
        """
        Get detailed information from a modem.

        Args:
            port_device: Port device path
            model_hint: Hint about the modem model

        Returns:
            Detailed modem information dictionary
        """
        try:
            # Use the new modem system instead of the old one
            try:
                from .modem_generic import GenericModem
                from .modem_base import ModemConfig
            except ImportError:
                from modem_generic import GenericModem
                from modem_base import ModemConfig

            # Create a temporary modem instance to get detailed info
            config = ModemConfig(
                baudrate=self.default_baudrate,
                timeout=self.default_timeout
            )

            modem = GenericModem(port_device, config)

            try:
                if modem.connect():
                    detailed_info = modem.get_detailed_info()
                    modem.disconnect()

                    # Add model hint if not already present
                    if detailed_info.get("model") in ["N/A", "UNKNOWN", "", "Unknown"]:
                        detailed_info["modem_name_config"] = model_hint
                        detailed_info["model"] = model_hint  # Use model hint as model name

                    # Always store the model hint for fallback
                    detailed_info["model_hint"] = model_hint

                    logger.info(f"Detailed info for {port_device}: {detailed_info}")
                    return detailed_info
                else:
                    return {
                        "port": port_device,
                        "status": "ERROR",
                        "connection_details": "Failed to connect to modem",
                        "modem_name_config": model_hint
                    }
            finally:
                # Ensure modem is disconnected
                try:
                    modem.disconnect()
                except:
                    pass

        except Exception as e:
            logger.error(f"Error getting detailed info for {port_device}: {e}")
            return {
                "port": port_device,
                "status": "ERROR",
                "connection_details": f"Error getting detailed info: {str(e)}",
                "modem_name_config": model_hint
            }

    def detect_modems(self) -> List[Dict[str, Any]]:
        """
        Detect all connected modems.
        
        Returns:
            List of detected modem information dictionaries
        """
        logger.info("Starting modem detection process...")
        
        if serial is None:
            logger.warning("pyserial not available. Cannot detect modems.")
            return []

        all_detected_modems = []
        usb_ports = self.scan_usb_ports()
        
        for port_info in usb_ports:
            port_device = port_info["device"]
            vid = port_info["vid"]
            pid = port_info["pid"]
            
            if vid is None or pid is None:
                logger.debug(f"Skipping port {port_device} - no VID/PID information")
                continue

            # Check if this is a known modem type
            modem_config = self.identify_modem_type(vid, pid)
            if not modem_config:
                logger.debug(f"Skipping port {port_device} - unknown VID:PID {vid:04X}:{pid:04X}")
                continue

            model_hint = modem_config["model_hint"]
            vid_str = f"{vid:04X}" if vid else "N/A"
            pid_str = f"{pid:04X}" if pid else "N/A"
            
            logger.info(f"Found potential modem: Port {port_device} (VID:{vid_str}, PID:{pid_str}, Hint: {model_hint})")
            
            # Probe the port
            modem_info = self.probe_modem_port(port_device, model_hint)
            if modem_info:
                # Add VID/PID information to the modem info
                modem_info["vid"] = vid
                modem_info["pid"] = pid
                modem_info["model_hint"] = model_hint
                modem_info["class_name"] = modem_config["class_name"]
                all_detected_modems.append(modem_info)

            # Add a small delay between probes to help modems recover
            time.sleep(0.5)

        logger.info(f"Modem detection complete. Found {len(all_detected_modems)} responsive modems.")
        return all_detected_modems

    def get_example_modem_data(self) -> List[Dict[str, Any]]:
        """
        Get example modem data for testing/fallback purposes.
        
        Returns:
            List of example modem data
        """
        logger.info("Providing example modem data.")
        return [
            {
                "id": "modem1_example",
                "name": "Modem 1 (Example)",
                "status": "Connected (Example)",
                "signal_strength": "-70 dBm",
                "operator": "ExampleNet",
                "sim_status": "READY",
                "imei": "112233445566778",
                "last_activity": "N/A",
                "recent_sms": [],
                "sms_mode_status": "Text",
                "port": "EXAMPLE_PORT_1",
                "cnmi_status": "OK"
            },
            {
                "id": "modem2_example",
                "name": "Modem 2 (Example)",
                "status": "Offline (Example)",
                "signal_strength": "N/A",
                "operator": "N/A",
                "sim_status": "NO SIM",
                "imei": "223344556677889",
                "last_activity": "N/A",
                "recent_sms": [],
                "sms_mode_status": "N/A",
                "port": "EXAMPLE_PORT_2",
                "cnmi_status": "N/A"
            }
        ]


# Global detector instance
_detector = None


def get_detector() -> ModemDetector:
    """Get the global modem detector instance."""
    global _detector
    if _detector is None:
        _detector = ModemDetector()
    return _detector


def detect_modems() -> List[Dict[str, Any]]:
    """
    Convenience function to detect modems using the global detector.
    
    Returns:
        List of detected modem information dictionaries
    """
    return get_detector().detect_modems()


def get_example_modem_data() -> List[Dict[str, Any]]:
    """
    Convenience function to get example modem data.
    
    Returns:
        List of example modem data
    """
    return get_detector().get_example_modem_data()
