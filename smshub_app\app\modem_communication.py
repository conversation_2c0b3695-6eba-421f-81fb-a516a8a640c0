"""
Modem communication module for the SMSHub application.

This module handles low-level serial communication with modems,
including AT command processing and response parsing.
"""

import logging
import threading
import time
from typing import List, Tuple, Optional

try:
    import serial
    import serial.tools.list_ports
except ImportError:
    serial = None

try:
    from .modem_base import BaseModem, ModemConfig, ModemInfo
except ImportError:
    from modem_base import BaseModem, ModemConfig, ModemInfo

logger = logging.getLogger(__name__)


class ModemCommunicator:
    """
    Handles low-level serial communication with modems.
    
    This class provides thread-safe communication with modems
    via serial ports using AT commands.
    """

    def __init__(self, port: str, baudrate: int = 115200, timeout: float = 1.0):
        """
        Initialize the modem communicator.
        
        Args:
            port: Serial port path
            baudrate: Communication speed
            timeout: Default timeout for operations
        """
        if serial is None:
            raise ImportError("pyserial library is not installed. Modem communication is not possible.")
        
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = serial.Serial()
        self.lock = threading.Lock()
        self._is_connected = False

    def connect(self) -> bool:
        """
        Establish serial connection to the modem.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            with self.lock:
                if self._is_connected:
                    logger.debug(f"Port {self.port} is already connected")
                    return True

                self.ser.port = self.port
                self.ser.baudrate = self.baudrate
                self.ser.timeout = self.timeout
                self.ser.write_timeout = self.timeout
                
                # Configure serial parameters for modem communication
                self.ser.bytesize = serial.EIGHTBITS
                self.ser.parity = serial.PARITY_NONE
                self.ser.stopbits = serial.STOPBITS_ONE
                self.ser.xonxoff = False
                self.ser.rtscts = False
                self.ser.dsrdtr = False

                self.ser.open()
                self._is_connected = True
                logger.info(f"Successfully connected to modem on port {self.port}")
                return True

        except Exception as e:
            logger.error(f"Failed to connect to port {self.port}: {e}")
            self._is_connected = False
            return False

    def disconnect(self) -> bool:
        """
        Close the serial connection.
        
        Returns:
            True if disconnection successful, False otherwise
        """
        try:
            with self.lock:
                if self.ser.is_open:
                    self.ser.close()
                self._is_connected = False
                logger.info(f"Disconnected from port {self.port}")
                return True
        except Exception as e:
            logger.error(f"Error disconnecting from port {self.port}: {e}")
            return False

    @property
    def is_connected(self) -> bool:
        """Check if the serial connection is active."""
        return self._is_connected and self.ser.is_open

    def send_at_command(self, command: str, expected_response: str = "OK", 
                       timeout_override: Optional[float] = None) -> Tuple[bool, List[str]]:
        """
        Send an AT command and wait for response.
        
        Args:
            command: AT command to send (without CR/LF)
            expected_response: Expected response pattern
            timeout_override: Override default timeout
            
        Returns:
            Tuple of (success, response_lines)
        """
        with self.lock:
            if not self.ser.is_open:
                logger.error(f"Port {self.port} is not open. Command '{command}' not sent.")
                return False, ["ERROR: Port not open"]

            full_command = command + '\r\n'
            response_lines = []
            success = False
            
            # Store original timeouts
            original_read_timeout = self.ser.timeout
            original_write_timeout = self.ser.write_timeout
            
            # Apply timeout override if provided
            current_timeout = timeout_override if timeout_override is not None else self.timeout
            self.ser.timeout = current_timeout
            self.ser.write_timeout = current_timeout

            try:
                # Clear input buffer
                self.ser.reset_input_buffer()
                
                # Send command
                logger.debug(f"Sending AT command to {self.port}: {command}")
                self.ser.write(full_command.encode('utf-8'))
                self.ser.flush()

                # Read response
                start_time = time.time()
                while time.time() - start_time < current_timeout:
                    try:
                        line = self.ser.readline().decode('utf-8', errors='ignore').strip()
                        if line:
                            response_lines.append(line)
                            logger.debug(f"Response from {self.port}: {line}")
                            
                            # Check for success/error conditions
                            if expected_response in line:
                                success = True
                                break
                            elif line in ["ERROR", "NO CARRIER", "BUSY"]:
                                success = False
                                break
                    except Exception as e:
                        logger.warning(f"Error reading response from {self.port}: {e}")
                        break

                if not response_lines:
                    response_lines = ["ERROR: No response received"]

            except Exception as e:
                logger.error(f"Error sending AT command '{command}' to {self.port}: {e}")
                response_lines = [f"ERROR: {str(e)}"]
                success = False

            finally:
                # Restore original timeouts
                self.ser.timeout = original_read_timeout
                self.ser.write_timeout = original_write_timeout

            logger.debug(f"AT command '{command}' on {self.port} - Success: {success}, Response: {response_lines}")
            return success, response_lines

    def read_available_data(self, timeout: float = 1.0) -> str:
        """
        Read any available data from the serial port.
        
        Args:
            timeout: Maximum time to wait for data
            
        Returns:
            Available data as string
        """
        with self.lock:
            if not self.ser.is_open:
                return ""

            original_timeout = self.ser.timeout
            self.ser.timeout = timeout
            
            try:
                data = ""
                start_time = time.time()
                
                while time.time() - start_time < timeout:
                    if self.ser.in_waiting > 0:
                        chunk = self.ser.read(self.ser.in_waiting).decode('utf-8', errors='ignore')
                        data += chunk
                    else:
                        time.sleep(0.1)
                
                return data
                
            except Exception as e:
                logger.error(f"Error reading available data from {self.port}: {e}")
                return ""
            finally:
                self.ser.timeout = original_timeout

    def flush_buffers(self) -> None:
        """Clear both input and output buffers."""
        try:
            with self.lock:
                if self.ser.is_open:
                    self.ser.reset_input_buffer()
                    self.ser.reset_output_buffer()
        except Exception as e:
            logger.warning(f"Error flushing buffers for {self.port}: {e}")


def check_modem_connection(communicator: ModemCommunicator) -> bool:
    """
    Test if a modem is responsive to basic AT commands.
    
    Args:
        communicator: ModemCommunicator instance
        
    Returns:
        True if modem responds, False otherwise
    """
    if not communicator.is_connected:
        return False

    # Try basic AT command
    success, _ = communicator.send_at_command("AT", timeout_override=2.0)
    if success:
        return True

    # Try ATI as fallback
    success, _ = communicator.send_at_command("ATI", timeout_override=2.0)
    return success


def execute_modem_command(communicator: ModemCommunicator, func_name: str, 
                         at_command: str, expected_in_response: str, 
                         parse_logic, timeout_override: float = None) -> Tuple[bool, any]:
    """
    Execute a modem command with parsing logic.
    
    Args:
        communicator: ModemCommunicator instance
        func_name: Name of the function for logging
        at_command: AT command to execute
        expected_in_response: Expected response pattern
        parse_logic: Function to parse the response
        timeout_override: Optional timeout override
        
    Returns:
        Tuple of (success, parsed_result)
    """
    if not isinstance(communicator, ModemCommunicator):
        return False, "ERROR: Invalid communicator"

    success, response = communicator.send_at_command(
        at_command, 
        expected_response=expected_in_response, 
        timeout_override=timeout_override
    )

    if func_name == "get_phone_number":
        logger.info(f"Raw response for {at_command} on {communicator.port} (func: {func_name}): {response}")

    if not success:
        logger.warning(f"{func_name} failed on {communicator.port}. Response: {response}")
        return False, f"Command failed: {response}"

    try:
        if parse_logic:
            parsed_result = parse_logic(response)
            return True, parsed_result
        else:
            return True, response
    except Exception as e:
        logger.error(f"Error parsing response for {func_name} on {communicator.port}: {e}")
        return False, f"Parse error: {str(e)}"
