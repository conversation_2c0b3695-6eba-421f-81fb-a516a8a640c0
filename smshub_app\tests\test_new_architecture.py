"""
Tests for the new modular architecture.

This module contains tests to verify that the new modular system
works correctly and maintains compatibility with the existing API.
"""

import pytest
import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the new modules
from smshub_app.app.modem_base import BaseModem, ModemConfig, ModemInfo
from smshub_app.app.modem_communication import ModemCommunicator
from smshub_app.app.modem_detection import ModemDetector
from smshub_app.app.sms_processing import SMSMessage, SMSParser, SMSProcessor
from smshub_app.app.config_enhanced import ConfigManager, AppConfig
from smshub_app.app.error_handling import ErrorHandler, ErrorInfo, ErrorCategory, ErrorSeverity
from smshub_app.app.modem_generic import GenericModem
from smshub_app.app.modem_manager_new import ModemManager


class TestModemBase:
    """Test the base modem classes."""
    
    def test_modem_info_creation(self):
        """Test ModemInfo dataclass creation."""
        info = ModemInfo(port="COM1", status="CONNECTED")
        assert info.port == "COM1"
        assert info.status == "CONNECTED"
        assert info.recent_sms == []
    
    def test_modem_config_creation(self):
        """Test ModemConfig dataclass creation."""
        config = ModemConfig()
        assert config.baudrate == 115200
        assert config.timeout == 1.0
        assert config.max_retries == 3


class TestModemCommunication:
    """Test the modem communication module."""
    
    @patch('smshub_app.app.modem_communication.serial')
    def test_communicator_creation(self, mock_serial):
        """Test ModemCommunicator creation."""
        mock_serial.Serial.return_value = Mock()
        
        comm = ModemCommunicator("COM1", 115200, 1.0)
        assert comm.port == "COM1"
        assert comm.baudrate == 115200
        assert comm.timeout == 1.0
    
    @patch('smshub_app.app.modem_communication.serial')
    def test_communicator_connect(self, mock_serial):
        """Test ModemCommunicator connection."""
        mock_ser = Mock()
        mock_serial.Serial.return_value = mock_ser
        mock_ser.open.return_value = None
        
        comm = ModemCommunicator("COM1")
        result = comm.connect()
        
        assert result is True
        mock_ser.open.assert_called_once()


class TestSMSProcessing:
    """Test the SMS processing module."""
    
    def test_sms_message_creation(self):
        """Test SMSMessage creation."""
        msg = SMSMessage(
            index=1,
            status="UNREAD",
            sender="+1234567890",
            timestamp="23/12/25,14:30:45+00",
            message_body="Test message",
            modem_port="COM1"
        )
        
        assert msg.index == 1
        assert msg.sender == "+1234567890"
        assert msg.message_body == "Test message"
    
    def test_sms_parser_cmgl(self):
        """Test CMGL response parsing."""
        response_lines = [
            '+CMGL: 1,"REC UNREAD","+1234567890",,"23/12/25,14:30:45+00"',
            'Test message content',
            'OK'
        ]
        
        parser = SMSParser()
        messages = parser.parse_cmgl_response(response_lines, "COM1")
        
        assert len(messages) == 1
        assert messages[0].sender == "+1234567890"
        assert messages[0].message_body == "Test message content"


class TestConfigEnhanced:
    """Test the enhanced configuration system."""
    
    def test_app_config_creation(self):
        """Test AppConfig creation with defaults."""
        config = AppConfig()
        assert config.server.port == 5000
        assert config.server.host == "0.0.0.0"
        assert config.scan_interval == 30
    
    @patch('smshub_app.app.config_enhanced.Path.exists')
    @patch('builtins.open')
    def test_config_manager_load(self, mock_open, mock_exists):
        """Test ConfigManager loading."""
        mock_exists.return_value = True
        mock_open.return_value.__enter__.return_value.read.return_value = '{"server_port": 8080}'
        
        manager = ConfigManager()
        config = manager.load()
        
        assert isinstance(config, AppConfig)


class TestErrorHandling:
    """Test the error handling system."""
    
    def test_error_info_creation(self):
        """Test ErrorInfo creation."""
        error = ErrorInfo(
            message="Test error",
            category=ErrorCategory.COMMUNICATION,
            severity=ErrorSeverity.HIGH,
            component="TestComponent",
            timestamp=1234567890
        )
        
        assert error.message == "Test error"
        assert error.category == ErrorCategory.COMMUNICATION
        assert error.severity == ErrorSeverity.HIGH
    
    def test_error_handler(self):
        """Test ErrorHandler functionality."""
        handler = ErrorHandler()
        
        error = ErrorInfo(
            message="Test error",
            category=ErrorCategory.COMMUNICATION,
            severity=ErrorSeverity.MEDIUM,
            component="TestComponent",
            timestamp=1234567890
        )
        
        handler.handle_error(error)
        
        assert len(handler.error_history) == 1
        assert handler.error_history[0].message == "Test error"


class TestModemGeneric:
    """Test the generic modem implementation."""
    
    @patch('smshub_app.app.modem_generic.ModemCommunicator')
    def test_generic_modem_creation(self, mock_comm_class):
        """Test GenericModem creation."""
        mock_comm = Mock()
        mock_comm_class.return_value = mock_comm
        
        modem = GenericModem("COM1")
        assert modem.port == "COM1"
        assert modem.model_hint == "Generic AT Modem"
    
    @patch('smshub_app.app.modem_generic.ModemCommunicator')
    def test_generic_modem_connect(self, mock_comm_class):
        """Test GenericModem connection."""
        mock_comm = Mock()
        mock_comm.connect.return_value = True
        mock_comm_class.return_value = mock_comm
        
        modem = GenericModem("COM1")
        result = modem.connect()
        
        assert result is True
        assert modem.is_connected is True


class TestModemManager:
    """Test the new modem manager."""
    
    @patch('smshub_app.app.modem_manager_new.get_enhanced_config')
    @patch('smshub_app.app.modem_manager_new.ModemDetector')
    def test_modem_manager_creation(self, mock_detector_class, mock_config):
        """Test ModemManager creation."""
        mock_config.return_value = AppConfig()
        mock_detector = Mock()
        mock_detector_class.return_value = mock_detector
        
        manager = ModemManager()
        assert manager.scan_interval == 30
        assert not manager.running
    
    @patch('smshub_app.app.modem_manager_new.get_enhanced_config')
    @patch('smshub_app.app.modem_manager_new.ModemDetector')
    def test_modem_manager_start_stop(self, mock_detector_class, mock_config):
        """Test ModemManager start and stop."""
        mock_config.return_value = AppConfig()
        mock_detector = Mock()
        mock_detector.detect_modems.return_value = []
        mock_detector_class.return_value = mock_detector
        
        manager = ModemManager()
        
        # Test start
        result = manager.start()
        assert result is True
        assert manager.running is True
        
        # Test stop
        result = manager.stop()
        assert result is True
        assert manager.running is False


class TestIntegration:
    """Integration tests for the new architecture."""
    
    @patch('smshub_app.app.modem_detection.serial')
    def test_modem_detection_integration(self, mock_serial):
        """Test integration between detection and communication."""
        # Mock serial port detection
        mock_port = Mock()
        mock_port.device = "COM1"
        mock_port.vid = 0x1410
        mock_port.pid = 0xB001
        mock_port.description = "Test Modem"
        mock_port.hwid = "USB\\VID_1410&PID_B001"
        mock_port.serial_number = "123456"
        mock_port.manufacturer = "Test"
        mock_port.product = "Test Modem"
        
        mock_serial.tools.list_ports.comports.return_value = [mock_port]
        
        # Mock serial communication
        mock_ser = Mock()
        mock_serial.Serial.return_value = mock_ser
        mock_ser.is_open = True
        
        detector = ModemDetector()
        ports = detector.scan_usb_ports()
        
        assert len(ports) == 1
        assert ports[0]["device"] == "COM1"
        assert ports[0]["vid"] == 0x1410
    
    def test_error_handling_integration(self):
        """Test error handling integration."""
        from smshub_app.app.error_handling import get_error_handler, report_error
        
        # Report an error
        report_error(
            "Test integration error",
            "TestComponent",
            ErrorCategory.COMMUNICATION,
            ErrorSeverity.MEDIUM
        )
        
        # Check that it was handled
        handler = get_error_handler()
        assert len(handler.error_history) > 0
        
        # Get stats
        stats = handler.get_error_stats()
        assert stats["total_errors"] > 0
        assert "communication" in stats["by_category"]


def test_compatibility():
    """Test that the new system maintains API compatibility."""
    # Test that we can import the expected functions
    try:
        from smshub_app.app.modem_manager_new import (
            get_modem_status,
            initialize_all_modems,
            fetch_and_store_new_sms_from_all_modems,
            shutdown_all_modem_readers
        )
        
        from smshub_app.app.config_enhanced import load_config
        
        # These should be callable (even if mocked)
        assert callable(get_modem_status)
        assert callable(initialize_all_modems)
        assert callable(fetch_and_store_new_sms_from_all_modems)
        assert callable(shutdown_all_modem_readers)
        assert callable(load_config)
        
    except ImportError as e:
        pytest.fail(f"Compatibility test failed: {e}")


if __name__ == "__main__":
    # Run tests when script is executed directly
    pytest.main([__file__, "-v"])
