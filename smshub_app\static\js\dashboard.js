document.addEventListener('DOMContentLoaded', function () {
    const modemCardsContainer = document.getElementById('modem-cards-container');
    const modemSelect = document.getElementById('modem-select');
    const smsMessagesTbody = document.getElementById('sms-messages-tbody');

    // Function to update modem status cards and select dropdown
    async function updateModemStatus() {
        try {
            const response = await fetch('/api/modem_status');
            if (!response.ok) {
                console.error('Failed to fetch modem status:', response.status, await response.text());
                modemCardsContainer.innerHTML = '<p class="text-red-500 col-span-full">Error loading modem status. Please check server logs.</p>';
                return;
            }
            const modems = await response.json();

            modemCardsContainer.innerHTML = ''; // Clear existing cards
            modemSelect.innerHTML = ''; // Clear existing options

            if (modems.length === 0) {
                modemCardsContainer.innerHTML = '<p class="text-gray-500 col-span-full">No modems detected or available.</p>';
                const defaultOption = document.createElement('option');
                defaultOption.textContent = 'No modems available';
                defaultOption.disabled = true;
                modemSelect.appendChild(defaultOption);
            } else {
                modems.forEach(modem => {
                    // Create modem card
                    const card = document.createElement('div');
                    card.className = 'bg-white p-6 rounded-lg shadow-lg'; // Light theme for cards
                    card.innerHTML = `
                        <h2 class="text-2xl font-semibold mb-3 text-gray-700">${modem.name || 'Unnamed Modem'} (ID: ${modem.id})</h2>
                        <div class="space-y-2 text-sm">
                            <p><strong>Status:</strong> <span class="${modem.status === 'RESPONSIVE' || modem.status === 'CONNECTED' || modem.status === 'Connected (Example)' ? 'text-green-600' : 'text-red-600'} font-semibold">${modem.status}</span></p>
                            <p><strong>Model:</strong> <span class="text-orange-600">${modem.model || 'N/A'}</span></p>
                            <p><strong>Signal Strength:</strong> <span class="text-blue-600">${modem.signal_strength || 'N/A'}</span></p>
                            <p><strong>Operator:</strong> <span class="text-purple-600">${modem.operator || 'N/A'}</span></p>
                            <p><strong>SIM Status:</strong> <span class="text-teal-600">${modem.sim_status || 'N/A'}</span></p>
                            <p><strong>Phone Number:</strong> <span class="text-pink-600">${modem.phone_number || 'N/A'}</span></p>
                            <p><strong>IMEI:</strong> <span class="text-gray-600">${modem.imei || 'N/A'}</span></p>
                            <p><strong>SMS Mode:</strong> <span class="text-indigo-600">${modem.sms_mode_status || 'N/A'}</span></p>
                            <p><strong>Last Activity:</strong> <span class="text-gray-500">${modem.last_activity || 'N/A'}</span></p>
                        </div>
                        ${modem.recent_sms && modem.recent_sms.length > 0 ? `
                        <div class="mt-4">
                            <h3 class="text-xl font-semibold mb-2 text-gray-700">Recent SMS (from card data)</h3>
                            <ul class="list-disc list-inside space-y-1 text-xs text-gray-600 max-h-24 overflow-y-auto">
                                ${modem.recent_sms.map(sms => `<li>From: ${sms.from} - ${sms.message.substring(0, 30)}...</li>`).join('')}
                            </ul>
                        </div>` : '<p class="mt-4 text-xs text-gray-500">No recent SMS in card data.</p>'}
                    `;
                    modemCardsContainer.appendChild(card);

                    // Populate modem select dropdown
                    if (modem.status === 'RESPONSIVE' || modem.status === 'CONNECTED') { // Only add usable modems
                        const option = document.createElement('option');
                        option.value = modem.id; // Or port if that's what the backend expects
                        option.textContent = `${modem.name || modem.id} (${modem.port || modem.id})`;
                        modemSelect.appendChild(option);
                    }
                });
                if (modemSelect.options.length === 0) {
                     const noResponsiveOption = document.createElement('option');
                    noResponsiveOption.textContent = 'No responsive modems';
                    noResponsiveOption.disabled = true;
                    modemSelect.appendChild(noResponsiveOption);
                }
            }
        } catch (error) {
            console.error('Error fetching or processing modem status:', error);
            modemCardsContainer.innerHTML = `<p class="text-red-500 col-span-full">Client-side error loading modem status: ${error.message}</p>`;
        }
        // After updating modem status, also update the SMS messages table
        updateSmsMessagesTable();
    }

    // Helper function to format modem's timestamp
    function formatDeviceTimestamp(timestampStr) {
        if (!timestampStr || typeof timestampStr !== 'string') {
            return 'N/A';
        }
        // Modem format: "yy/MM/dd,HH:mm:ss±zz" e.g., "25/05/26,17:21:24+00"
        // Needs to be transformed for reliable Date parsing, e.g., to ISO 8601 like
        // "20yy-MM-ddTHH:mm:ss±zz:00" (if zz is just hours) or handle timezone properly.

        const parts = timestampStr.match(/(\d{2})\/(\d{2})\/(\d{2}),(\d{2}):(\d{2}):(\d{2})([+-])(\d{2})/);
        if (parts) {
            // Assuming the year 'yy' is in the 21st century
            const year = `20${parts[1]}`;
            const month = parts[2];
            const day = parts[3];
            const hours = parts[4];
            const minutes = parts[5];
            const seconds = parts[6];
            const tzSign = parts[7];
            const tzHours = parts[8];
            
            // Construct a string that's more likely to be parsed correctly by new Date()
            // ISO 8601 format: YYYY-MM-DDTHH:mm:ssZ or YYYY-MM-DDTHH:mm:ss±hh:mm
            // The modem's ±zz is just hours, so we add :00 for minutes.
            const isoLikeTimestamp = `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${tzSign}${tzHours}:00`;
            
            const dateObj = new Date(isoLikeTimestamp);
            if (isNaN(dateObj.getTime())) { // Check if date is invalid
                console.warn(`Could not parse transformed device timestamp: ${isoLikeTimestamp} (original: ${timestampStr})`);
                return 'Invalid Date'; // Fallback if parsing still fails
            }
            return dateObj.toLocaleString();
        } else {
            console.warn(`Could not parse device timestamp format: ${timestampStr}`);
            return 'Invalid Date Format'; // Original string was not in expected format
        }
    }

    // Function to update the SMS messages table
    async function updateSmsMessagesTable() {
        if (!smsMessagesTbody) {
            console.warn('SMS messages table body not found (sms-messages-tbody).');
            return;
        }
        try {
            const response = await fetch('/api/sms_messages');
            if (!response.ok) {
                console.error('Failed to fetch SMS messages:', response.status, await response.text());
                smsMessagesTbody.innerHTML = `<tr><td colspan="8" class="px-6 py-4 text-center text-red-500">Error loading SMS messages.</td></tr>`;
                return;
            }
            const messages = await response.json();
            smsMessagesTbody.innerHTML = ''; // Clear existing rows

            if (messages.length === 0) {
                smsMessagesTbody.innerHTML = `<tr><td colspan="9" class="px-6 py-4 text-center text-gray-500">No SMS messages found in the database.</td></tr>`;
            } else {
                messages.forEach(msg => {
                    const row = smsMessagesTbody.insertRow();
                    row.className = 'hover:bg-gray-100'; // Light hover for light theme table

                    // Helper to create and append a cell
                    const createCell = (text, cssClass = 'px-6 py-4 whitespace-nowrap text-sm text-gray-700') => {
                        const cell = row.insertCell();
                        cell.textContent = text !== null && typeof text !== 'undefined' ? text : 'N/A';
                        cell.className = cssClass;
                        if (cssClass.includes('message-body-cell')) { // Special handling for message body
                            cell.style.maxWidth = '300px'; // Max width for message
                            cell.style.overflow = 'hidden';
                            cell.style.textOverflow = 'ellipsis';
                            cell.title = text; // Show full message on hover
                        }
                        return cell;
                    };

                    createCell(msg.id);
                    createCell(msg.modem_port);
                    createCell(msg.receiving_modem_phone_number); // Display the new field
                    createCell(msg.sender);
                    createCell(msg.message_body, 'px-6 py-4 text-sm text-gray-700 message-body-cell'); // Apply special class
                    createCell(formatDeviceTimestamp(msg.timestamp_device));
                    createCell(msg.timestamp_received_app ? new Date(msg.timestamp_received_app).toLocaleString() : 'N/A');
                    createCell(msg.status_on_modem);
                    createCell(msg.forwarding_status);
                });
            }
        } catch (error) {
            console.error('Error fetching or processing SMS messages:', error);
            smsMessagesTbody.innerHTML = `<tr><td colspan="9" class="px-6 py-4 text-center text-red-500">Client-side error loading SMS: ${error.message}</td></tr>`;
        }
    }
    
    // Send SMS form submission
    const sendSmsForm = document.getElementById('send-sms-form');
    const sendSmsStatusDiv = document.getElementById('send-sms-status');

    if (sendSmsForm) {
        sendSmsForm.addEventListener('submit', async function (event) {
            event.preventDefault();
            sendSmsStatusDiv.textContent = 'Sending...';
            sendSmsStatusDiv.className = 'mt-4 text-sm text-blue-600';

            const formData = new FormData(sendSmsForm);
            const data = Object.fromEntries(formData.entries());

            try {
                // This endpoint doesn't exist yet, placeholder for future.
                // const response = await fetch('/api/send_sms', { 
                //     method: 'POST',
                //     headers: { 'Content-Type': 'application/json' },
                //     body: JSON.stringify(data)
                // });
                // if (response.ok) {
                //     const result = await response.json();
                //     sendSmsStatusDiv.textContent = `SMS sent successfully! Message ID: ${result.message_id || 'N/A'}`;
                //     sendSmsStatusDiv.className = 'mt-4 text-sm text-green-600';
                //     sendSmsForm.reset();
                // } else {
                //     const errorResult = await response.json();
                //     sendSmsStatusDiv.textContent = `Error sending SMS: ${errorResult.error || response.statusText}`;
                //     sendSmsStatusDiv.className = 'mt-4 text-sm text-red-600';
                // }
                // Placeholder until send SMS API is implemented
                console.log("Send SMS form submitted with data:", data);
                sendSmsStatusDiv.textContent = 'Send SMS functionality is not yet implemented.';
                sendSmsStatusDiv.className = 'mt-4 text-sm text-orange-600';

            } catch (error) {
                console.error('Error submitting send SMS form:', error);
                sendSmsStatusDiv.textContent = 'Client-side error: Could not send SMS.';
                sendSmsStatusDiv.className = 'mt-4 text-sm text-red-600';
            }
        });
    } else {
        console.warn('Send SMS form not found.');
    }

    // Initial data load
    updateModemStatus(); // This will also call updateSmsMessagesTable

    // Set interval to periodically update data (e.g., every 10 seconds)
    // setInterval(updateModemStatus, 10000); // 10 seconds
    // For now, let's keep it to manual refresh or on-load to avoid too many requests during dev
    console.log("Dashboard JS loaded. Initial modem status and SMS table update triggered.");
});