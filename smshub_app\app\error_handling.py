"""
Centralized error handling module for the SMSHub application.

This module provides consistent error handling, retry mechanisms,
and error reporting across the application.
"""

import logging
import time
import traceback
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass
from functools import wraps

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    COMMUNICATION = "communication"
    CONFIGURATION = "configuration"
    HARDWARE = "hardware"
    NETWORK = "network"
    DATABASE = "database"
    VALIDATION = "validation"
    TIMEOUT = "timeout"
    PERMISSION = "permission"
    UNKNOWN = "unknown"


@dataclass
class ErrorInfo:
    """Structured error information."""
    message: str
    category: ErrorCategory
    severity: ErrorSeverity
    component: str
    timestamp: float
    exception: Optional[Exception] = None
    context: Dict[str, Any] = None
    retry_count: int = 0
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}
        if self.timestamp == 0:
            self.timestamp = time.time()


class RetryConfig:
    """Configuration for retry mechanisms."""
    
    def __init__(self, max_attempts: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, exponential_base: float = 2.0,
                 jitter: bool = True):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter


class ErrorHandler:
    """Centralized error handler with retry and reporting capabilities."""
    
    def __init__(self):
        self.error_history: List[ErrorInfo] = []
        self.max_history_size = 1000
        self.error_callbacks: Dict[ErrorCategory, List[Callable]] = {}
    
    def register_error_callback(self, category: ErrorCategory, callback: Callable):
        """Register a callback for specific error categories."""
        if category not in self.error_callbacks:
            self.error_callbacks[category] = []
        self.error_callbacks[category].append(callback)
    
    def handle_error(self, error_info: ErrorInfo) -> None:
        """Handle an error with logging and callbacks."""
        # Add to history
        self.error_history.append(error_info)
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
        
        # Log the error
        self._log_error(error_info)
        
        # Execute callbacks
        self._execute_callbacks(error_info)
    
    def _log_error(self, error_info: ErrorInfo) -> None:
        """Log error information with appropriate level."""
        log_message = (f"[{error_info.component}] {error_info.message} "
                      f"(Category: {error_info.category.value}, "
                      f"Severity: {error_info.severity.value})")
        
        if error_info.context:
            log_message += f" Context: {error_info.context}"
        
        if error_info.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_message, exc_info=error_info.exception)
        elif error_info.severity == ErrorSeverity.HIGH:
            logger.error(log_message, exc_info=error_info.exception)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
    
    def _execute_callbacks(self, error_info: ErrorInfo) -> None:
        """Execute registered callbacks for the error category."""
        callbacks = self.error_callbacks.get(error_info.category, [])
        for callback in callbacks:
            try:
                callback(error_info)
            except Exception as e:
                logger.error(f"Error in error callback: {e}")
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        if not self.error_history:
            return {"total_errors": 0}
        
        stats = {
            "total_errors": len(self.error_history),
            "by_category": {},
            "by_severity": {},
            "by_component": {},
            "recent_errors": len([e for e in self.error_history 
                                if time.time() - e.timestamp < 3600])  # Last hour
        }
        
        for error in self.error_history:
            # By category
            cat = error.category.value
            stats["by_category"][cat] = stats["by_category"].get(cat, 0) + 1
            
            # By severity
            sev = error.severity.value
            stats["by_severity"][sev] = stats["by_severity"].get(sev, 0) + 1
            
            # By component
            comp = error.component
            stats["by_component"][comp] = stats["by_component"].get(comp, 0) + 1
        
        return stats


class RetryManager:
    """Manages retry logic with exponential backoff."""
    
    def __init__(self, config: RetryConfig = None):
        self.config = config or RetryConfig()
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for the given attempt number."""
        if attempt <= 0:
            return 0
        
        delay = self.config.base_delay * (self.config.exponential_base ** (attempt - 1))
        delay = min(delay, self.config.max_delay)
        
        if self.config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay
    
    def should_retry(self, attempt: int, exception: Exception = None) -> bool:
        """Determine if operation should be retried."""
        if attempt >= self.config.max_attempts:
            return False
        
        # Add logic for non-retryable exceptions
        if exception and isinstance(exception, (KeyboardInterrupt, SystemExit)):
            return False
        
        return True


def with_error_handling(component: str, category: ErrorCategory = ErrorCategory.UNKNOWN,
                       severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                       retry_config: RetryConfig = None):
    """Decorator for automatic error handling and retry."""
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            retry_manager = RetryManager(retry_config) if retry_config else None
            attempt = 0
            last_exception = None
            
            while True:
                attempt += 1
                try:
                    return func(*args, **kwargs)
                
                except Exception as e:
                    last_exception = e
                    
                    # Create error info
                    error_info = ErrorInfo(
                        message=f"Error in {func.__name__}: {str(e)}",
                        category=category,
                        severity=severity,
                        component=component,
                        timestamp=time.time(),
                        exception=e,
                        context={"function": func.__name__, "attempt": attempt},
                        retry_count=attempt - 1
                    )
                    
                    # Handle the error
                    get_error_handler().handle_error(error_info)
                    
                    # Check if we should retry
                    if retry_manager and retry_manager.should_retry(attempt, e):
                        delay = retry_manager.calculate_delay(attempt)
                        logger.info(f"Retrying {func.__name__} in {delay:.2f} seconds (attempt {attempt + 1})")
                        time.sleep(delay)
                        continue
                    else:
                        # No more retries, re-raise the exception
                        raise e
        
        return wrapper
    return decorator


def with_timeout(timeout_seconds: float, component: str = "unknown"):
    """Decorator to add timeout to functions."""
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError(f"Function {func.__name__} timed out after {timeout_seconds} seconds")
            
            # Set up timeout
            old_handler = signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(int(timeout_seconds))
            
            try:
                result = func(*args, **kwargs)
                signal.alarm(0)  # Cancel timeout
                return result
            except TimeoutError as e:
                error_info = ErrorInfo(
                    message=str(e),
                    category=ErrorCategory.TIMEOUT,
                    severity=ErrorSeverity.HIGH,
                    component=component,
                    timestamp=time.time(),
                    exception=e,
                    context={"function": func.__name__, "timeout": timeout_seconds}
                )
                get_error_handler().handle_error(error_info)
                raise
            finally:
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return=None, 
                component: str = "unknown", **kwargs) -> Tuple[bool, Any]:
    """
    Safely execute a function with error handling.
    
    Returns:
        Tuple of (success, result)
    """
    try:
        result = func(*args, **kwargs)
        return True, result
    except Exception as e:
        error_info = ErrorInfo(
            message=f"Error in safe_execute: {str(e)}",
            category=ErrorCategory.UNKNOWN,
            severity=ErrorSeverity.MEDIUM,
            component=component,
            timestamp=time.time(),
            exception=e,
            context={"function": getattr(func, '__name__', 'unknown')}
        )
        get_error_handler().handle_error(error_info)
        return False, default_return


def create_error_info(message: str, component: str, 
                     category: ErrorCategory = ErrorCategory.UNKNOWN,
                     severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                     exception: Exception = None,
                     context: Dict[str, Any] = None) -> ErrorInfo:
    """Create an ErrorInfo object with current timestamp."""
    return ErrorInfo(
        message=message,
        category=category,
        severity=severity,
        component=component,
        timestamp=time.time(),
        exception=exception,
        context=context or {}
    )


# Global error handler instance
_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler


def report_error(message: str, component: str, 
                category: ErrorCategory = ErrorCategory.UNKNOWN,
                severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                exception: Exception = None,
                context: Dict[str, Any] = None) -> None:
    """Convenience function to report an error."""
    error_info = create_error_info(message, component, category, severity, exception, context)
    get_error_handler().handle_error(error_info)
