"""
Resource optimization module for the SMSHub application.

This module provides memory and CPU optimization, resource monitoring,
and background task optimization for improved system performance.
"""

import gc
import os
import psutil
import threading
import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta

try:
    from .logging_enhanced import get_logger, operation_timer
    from .error_handling import with_error_handling, ErrorCategory, ErrorSeverity, report_error
    from .config_enhanced import get_enhanced_config
except ImportError:
    from logging_enhanced import get_logger, operation_timer
    from error_handling import with_error_handling, ErrorCategory, ErrorSeverity, report_error
    from config_enhanced import get_enhanced_config

logger = get_logger(__name__, "ResourceOptimizer")


@dataclass
class ResourceMetrics:
    """Resource usage metrics."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_usage_percent: float
    thread_count: int
    open_files: int


@dataclass
class OptimizationConfig:
    """Configuration for resource optimization."""
    memory_threshold: float = 80.0  # Percentage
    cpu_threshold: float = 90.0     # Percentage
    disk_threshold: float = 85.0    # Percentage
    gc_interval: int = 300          # Seconds (5 minutes)
    monitoring_interval: int = 60   # Seconds (1 minute)
    max_metrics_history: int = 1440 # Keep 24 hours of minute-by-minute data


class MemoryOptimizer:
    """
    Memory usage optimization and monitoring.
    
    Provides memory profiling, garbage collection optimization,
    and memory leak detection.
    """

    def __init__(self):
        """Initialize memory optimizer."""
        self.config = OptimizationConfig()
        self._last_gc_time = time.time()

    @with_error_handling("MemoryOptimizer", ErrorCategory.UNKNOWN, ErrorSeverity.LOW)
    def get_memory_usage(self) -> Dict[str, Any]:
        """
        Get detailed memory usage information.
        
        Returns:
            Memory usage statistics
        """
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            
            # System memory info
            system_memory = psutil.virtual_memory()
            
            return {
                "process": {
                    "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
                    "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
                    "percent": round(memory_percent, 2),
                    "num_threads": process.num_threads(),
                    "open_files": len(process.open_files())
                },
                "system": {
                    "total_mb": round(system_memory.total / 1024 / 1024, 2),
                    "available_mb": round(system_memory.available / 1024 / 1024, 2),
                    "used_mb": round(system_memory.used / 1024 / 1024, 2),
                    "percent": round(system_memory.percent, 2)
                },
                "gc_stats": self._get_gc_stats()
            }
            
        except Exception as e:
            logger.error(f"Error getting memory usage: {e}")
            return {"error": str(e)}

    def _get_gc_stats(self) -> Dict[str, Any]:
        """Get garbage collection statistics."""
        try:
            return {
                "collections": gc.get_count(),
                "threshold": gc.get_threshold(),
                "stats": gc.get_stats() if hasattr(gc, 'get_stats') else None
            }
        except Exception as e:
            logger.error(f"Error getting GC stats: {e}")
            return {"error": str(e)}

    @with_error_handling("MemoryOptimizer", ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM)
    def optimize_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        Optimize memory usage through garbage collection and cleanup.
        
        Args:
            force: Force optimization regardless of thresholds
            
        Returns:
            Optimization results
        """
        current_time = time.time()
        
        # Check if optimization is needed
        if not force and (current_time - self._last_gc_time) < self.config.gc_interval:
            return {"skipped": "Too soon since last optimization"}

        with operation_timer(logger, "memory_optimization"):
            before_memory = self.get_memory_usage()
            
            # Force garbage collection
            collected = []
            for generation in range(3):
                collected.append(gc.collect(generation))
            
            # Additional cleanup
            gc.collect()
            
            after_memory = self.get_memory_usage()
            self._last_gc_time = current_time
            
            result = {
                "timestamp": current_time,
                "collections_performed": collected,
                "memory_before_mb": before_memory.get("process", {}).get("rss_mb", 0),
                "memory_after_mb": after_memory.get("process", {}).get("rss_mb", 0),
                "memory_freed_mb": (
                    before_memory.get("process", {}).get("rss_mb", 0) - 
                    after_memory.get("process", {}).get("rss_mb", 0)
                )
            }
            
            logger.info(f"Memory optimization completed: freed {result['memory_freed_mb']:.2f} MB")
            return result

    def check_memory_threshold(self) -> bool:
        """
        Check if memory usage exceeds threshold.
        
        Returns:
            True if threshold exceeded
        """
        try:
            memory_info = self.get_memory_usage()
            memory_percent = memory_info.get("process", {}).get("percent", 0)
            
            if memory_percent > self.config.memory_threshold:
                logger.warning(f"Memory usage ({memory_percent:.1f}%) exceeds threshold ({self.config.memory_threshold}%)")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking memory threshold: {e}")
            return False


class CPUOptimizer:
    """
    CPU usage optimization and monitoring.
    
    Provides CPU profiling and optimization recommendations.
    """

    def __init__(self):
        """Initialize CPU optimizer."""
        self.config = OptimizationConfig()

    @with_error_handling("CPUOptimizer", ErrorCategory.UNKNOWN, ErrorSeverity.LOW)
    def get_cpu_usage(self) -> Dict[str, Any]:
        """
        Get detailed CPU usage information.
        
        Returns:
            CPU usage statistics
        """
        try:
            process = psutil.Process()
            
            # Get CPU usage over a short interval
            cpu_percent = process.cpu_percent(interval=1.0)
            
            # System CPU info
            system_cpu = psutil.cpu_percent(interval=None, percpu=True)
            
            return {
                "process": {
                    "cpu_percent": round(cpu_percent, 2),
                    "num_threads": process.num_threads(),
                    "cpu_times": process.cpu_times()._asdict()
                },
                "system": {
                    "cpu_percent": round(psutil.cpu_percent(), 2),
                    "cpu_count": psutil.cpu_count(),
                    "cpu_count_logical": psutil.cpu_count(logical=True),
                    "per_cpu": [round(cpu, 2) for cpu in system_cpu],
                    "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else None
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting CPU usage: {e}")
            return {"error": str(e)}

    def check_cpu_threshold(self) -> bool:
        """
        Check if CPU usage exceeds threshold.
        
        Returns:
            True if threshold exceeded
        """
        try:
            cpu_info = self.get_cpu_usage()
            cpu_percent = cpu_info.get("process", {}).get("cpu_percent", 0)
            
            if cpu_percent > self.config.cpu_threshold:
                logger.warning(f"CPU usage ({cpu_percent:.1f}%) exceeds threshold ({self.config.cpu_threshold}%)")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking CPU threshold: {e}")
            return False


class ResourceMonitor:
    """
    Comprehensive resource monitoring system.
    
    Continuously monitors system resources and triggers
    optimization when thresholds are exceeded.
    """

    def __init__(self):
        """Initialize resource monitor."""
        self.config = OptimizationConfig()
        self.memory_optimizer = MemoryOptimizer()
        self.cpu_optimizer = CPUOptimizer()
        self.metrics_history: List[ResourceMetrics] = []
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._optimization_callbacks: List[Callable] = []

    def start_monitoring(self) -> bool:
        """
        Start resource monitoring.
        
        Returns:
            True if monitoring started successfully
        """
        if self._monitoring:
            return True

        try:
            self._monitoring = True
            self._monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True,
                name="ResourceMonitor"
            )
            self._monitor_thread.start()
            
            logger.info("Resource monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting resource monitoring: {e}")
            self._monitoring = False
            return False

    def stop_monitoring(self) -> None:
        """Stop resource monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Resource monitoring stopped")

    def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self._monitoring:
            try:
                # Collect metrics
                metrics = self._collect_metrics()
                if metrics:
                    self.metrics_history.append(metrics)
                    
                    # Keep only recent metrics
                    if len(self.metrics_history) > self.config.max_metrics_history:
                        self.metrics_history = self.metrics_history[-self.config.max_metrics_history:]
                    
                    # Check thresholds and trigger optimization
                    self._check_thresholds(metrics)
                
                time.sleep(self.config.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.config.monitoring_interval)

    def _collect_metrics(self) -> Optional[ResourceMetrics]:
        """Collect current resource metrics."""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            # Get disk usage for current directory
            disk_usage = psutil.disk_usage('.')
            
            return ResourceMetrics(
                timestamp=time.time(),
                cpu_percent=process.cpu_percent(),
                memory_percent=process.memory_percent(),
                memory_mb=memory_info.rss / 1024 / 1024,
                disk_usage_percent=(disk_usage.used / disk_usage.total) * 100,
                thread_count=process.num_threads(),
                open_files=len(process.open_files())
            )
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
            return None

    def _check_thresholds(self, metrics: ResourceMetrics) -> None:
        """Check if any thresholds are exceeded and trigger optimization."""
        try:
            # Check memory threshold
            if metrics.memory_percent > self.config.memory_threshold:
                logger.warning(f"Memory threshold exceeded: {metrics.memory_percent:.1f}%")
                self.memory_optimizer.optimize_memory()
                self._trigger_callbacks("memory_threshold", metrics)
            
            # Check CPU threshold
            if metrics.cpu_percent > self.config.cpu_threshold:
                logger.warning(f"CPU threshold exceeded: {metrics.cpu_percent:.1f}%")
                self._trigger_callbacks("cpu_threshold", metrics)
            
            # Check disk threshold
            if metrics.disk_usage_percent > self.config.disk_threshold:
                logger.warning(f"Disk threshold exceeded: {metrics.disk_usage_percent:.1f}%")
                self._trigger_callbacks("disk_threshold", metrics)
                
        except Exception as e:
            logger.error(f"Error checking thresholds: {e}")

    def _trigger_callbacks(self, threshold_type: str, metrics: ResourceMetrics) -> None:
        """Trigger optimization callbacks."""
        for callback in self._optimization_callbacks:
            try:
                callback(threshold_type, metrics)
            except Exception as e:
                logger.error(f"Error in optimization callback: {e}")

    def add_optimization_callback(self, callback: Callable) -> None:
        """
        Add callback to be called when thresholds are exceeded.
        
        Args:
            callback: Function to call with (threshold_type, metrics)
        """
        self._optimization_callbacks.append(callback)

    def get_current_metrics(self) -> Optional[ResourceMetrics]:
        """Get current resource metrics."""
        return self._collect_metrics()

    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get summary of metrics for the specified time period.
        
        Args:
            hours: Number of hours to include in summary
            
        Returns:
            Metrics summary
        """
        if not self.metrics_history:
            return {"error": "No metrics available"}

        cutoff_time = time.time() - (hours * 3600)
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return {"error": f"No metrics available for last {hours} hours"}

        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_percent for m in recent_metrics]
        memory_mb_values = [m.memory_mb for m in recent_metrics]
        
        return {
            "time_period_hours": hours,
            "sample_count": len(recent_metrics),
            "cpu": {
                "avg": round(sum(cpu_values) / len(cpu_values), 2),
                "min": round(min(cpu_values), 2),
                "max": round(max(cpu_values), 2)
            },
            "memory": {
                "avg_percent": round(sum(memory_values) / len(memory_values), 2),
                "min_percent": round(min(memory_values), 2),
                "max_percent": round(max(memory_values), 2),
                "avg_mb": round(sum(memory_mb_values) / len(memory_mb_values), 2),
                "min_mb": round(min(memory_mb_values), 2),
                "max_mb": round(max(memory_mb_values), 2)
            },
            "latest": {
                "cpu_percent": recent_metrics[-1].cpu_percent,
                "memory_percent": recent_metrics[-1].memory_percent,
                "memory_mb": round(recent_metrics[-1].memory_mb, 2),
                "thread_count": recent_metrics[-1].thread_count,
                "open_files": recent_metrics[-1].open_files
            }
        }


class BackgroundTaskOptimizer:
    """
    Background task optimization.
    
    Optimizes background tasks to reduce resource usage
    and improve overall system performance.
    """

    def __init__(self):
        """Initialize background task optimizer."""
        self.task_stats: Dict[str, Dict[str, Any]] = {}

    def register_task(self, task_name: str, execution_time: float, 
                     cpu_usage: float = 0, memory_usage: float = 0) -> None:
        """
        Register task execution statistics.
        
        Args:
            task_name: Name of the task
            execution_time: Task execution time in seconds
            cpu_usage: CPU usage during task
            memory_usage: Memory usage during task
        """
        if task_name not in self.task_stats:
            self.task_stats[task_name] = {
                "executions": 0,
                "total_time": 0,
                "avg_time": 0,
                "max_time": 0,
                "min_time": float('inf'),
                "total_cpu": 0,
                "total_memory": 0
            }

        stats = self.task_stats[task_name]
        stats["executions"] += 1
        stats["total_time"] += execution_time
        stats["avg_time"] = stats["total_time"] / stats["executions"]
        stats["max_time"] = max(stats["max_time"], execution_time)
        stats["min_time"] = min(stats["min_time"], execution_time)
        stats["total_cpu"] += cpu_usage
        stats["total_memory"] += memory_usage

    def get_task_recommendations(self) -> List[Dict[str, Any]]:
        """
        Get optimization recommendations for background tasks.
        
        Returns:
            List of optimization recommendations
        """
        recommendations = []
        
        for task_name, stats in self.task_stats.items():
            if stats["executions"] == 0:
                continue
                
            avg_time = stats["avg_time"]
            max_time = stats["max_time"]
            
            # Recommend optimization for slow tasks
            if avg_time > 10:  # Tasks taking more than 10 seconds on average
                recommendations.append({
                    "task": task_name,
                    "type": "performance",
                    "message": f"Task takes {avg_time:.2f}s on average, consider optimization",
                    "priority": "high" if avg_time > 30 else "medium"
                })
            
            # Recommend scheduling optimization for frequent tasks
            if stats["executions"] > 100 and max_time > avg_time * 3:
                recommendations.append({
                    "task": task_name,
                    "type": "scheduling",
                    "message": f"Task has variable execution time ({avg_time:.2f}s avg, {max_time:.2f}s max)",
                    "priority": "medium"
                })

        return recommendations

    def get_task_stats(self) -> Dict[str, Any]:
        """Get all task statistics."""
        return self.task_stats.copy()


# Global instances
_resource_monitor: Optional[ResourceMonitor] = None
_memory_optimizer: Optional[MemoryOptimizer] = None
_cpu_optimizer: Optional[CPUOptimizer] = None
_task_optimizer: Optional[BackgroundTaskOptimizer] = None


def get_resource_monitor() -> ResourceMonitor:
    """Get the global resource monitor instance."""
    global _resource_monitor
    if _resource_monitor is None:
        _resource_monitor = ResourceMonitor()
    return _resource_monitor


def get_memory_optimizer() -> MemoryOptimizer:
    """Get the global memory optimizer instance."""
    global _memory_optimizer
    if _memory_optimizer is None:
        _memory_optimizer = MemoryOptimizer()
    return _memory_optimizer


def get_cpu_optimizer() -> CPUOptimizer:
    """Get the global CPU optimizer instance."""
    global _cpu_optimizer
    if _cpu_optimizer is None:
        _cpu_optimizer = CPUOptimizer()
    return _cpu_optimizer


def get_task_optimizer() -> BackgroundTaskOptimizer:
    """Get the global task optimizer instance."""
    global _task_optimizer
    if _task_optimizer is None:
        _task_optimizer = BackgroundTaskOptimizer()
    return _task_optimizer
