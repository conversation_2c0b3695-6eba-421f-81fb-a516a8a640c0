import json
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_config():
    """
    Loads the configuration from the config.json file.

    The config.json file is expected to be in the parent directory of this script's location.
    (i.e., if this script is in 'smshub_app/app/', config.json should be in 'smshub_app/').

    Returns:
        dict: The parsed JSON content from config.json.
              Returns an empty dictionary if the file is not found or if there's an error parsing it.
    """
    # Determine the absolute path to the directory containing this script
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # Construct the path to config.json, which is in the parent directory
    config_file_path = os.path.join(current_dir, '..', 'config.json')

    try:
        with open(config_file_path, 'r', encoding='utf-8') as f: # Added encoding for robustness
            config = json.load(f)
            logging.info(f"Successfully loaded configuration from {config_file_path}")
            return config
    except FileNotFoundError:
        logging.error(f"Configuration file not found at {config_file_path}. Returning empty configuration.")
        return {}
    except json.JSONDecodeError as e:
        logging.error(f"Error decoding JSON from {config_file_path}: {e}. Returning empty configuration.")
        return {}
    except Exception as e:
        logging.error(f"An unexpected error occurred while loading configuration from {config_file_path}: {e}. Returning empty configuration.")
        return {}

if __name__ == '__main__':
    # Example usage:
    config_data = load_config()
    if config_data:
        print("Configuration loaded successfully:")
        # Example: print a couple of values if they exist
        if 'smshub_api_key' in config_data:
            print(f"SMSHub API Key: {config_data.get('smshub_api_key')}")
        if 'log_level' in config_data:
            print(f"Log Level: {config_data.get('log_level')}")
        # print(json.dumps(config_data, indent=2)) # Uncomment for full config view
    else:
        print("Failed to load configuration or configuration is empty.")