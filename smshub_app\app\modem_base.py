"""
Base modem classes and interfaces for the SMSHub application.

This module provides the abstract base class and common functionality
for all modem implementations.
"""

import logging
import threading
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ModemInfo:
    """Data class to hold modem information."""
    port: str
    status: str
    connection_details: str = "N/A"
    sim_status: str = "N/A"
    signal_rssi: str = "N/A"
    signal_ber: str = "N/A"
    operator: str = "N/A"
    phone_number: str = "N/A"
    imei: str = "N/A"
    model: str = "N/A"
    sms_mode_status: str = "N/A"
    cnmi_status: str = "N/A"
    last_activity: str = "N/A"
    recent_sms: List[Dict[str, Any]] = None

    def __post_init__(self):
        if self.recent_sms is None:
            self.recent_sms = []


@dataclass
class ModemConfig:
    """Configuration for modem operations."""
    baudrate: int = 115200
    timeout: float = 1.0
    scan_interval: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    sms_check_interval: int = 10


class BaseModem(ABC):
    """
    Abstract base class for all modem implementations.
    
    This class defines the interface that all modem types must implement
    and provides common functionality for modem operations.
    """

    def __init__(self, port: str, config: ModemConfig = None):
        """
        Initialize the base modem.
        
        Args:
            port: Serial port for the modem
            config: Configuration object for modem operations
        """
        self.port = port
        self.config = config or ModemConfig()
        self.lock = threading.Lock()
        self._is_connected = False
        self._last_error = None
        self._info = ModemInfo(port=port, status="UNKNOWN")
        
        # Modem-specific identifiers
        self.vendor_id: Optional[int] = None
        self.product_id: Optional[int] = None
        self.model_hint: str = "Unknown"

    @property
    def is_connected(self) -> bool:
        """Check if modem is currently connected."""
        return self._is_connected

    @property
    def last_error(self) -> Optional[str]:
        """Get the last error that occurred."""
        return self._last_error

    @property
    def info(self) -> ModemInfo:
        """Get current modem information."""
        return self._info

    @abstractmethod
    def connect(self) -> bool:
        """
        Establish connection to the modem.
        
        Returns:
            True if connection successful, False otherwise
        """
        pass

    @abstractmethod
    def disconnect(self) -> bool:
        """
        Disconnect from the modem.
        
        Returns:
            True if disconnection successful, False otherwise
        """
        pass

    @abstractmethod
    def send_at_command(self, command: str, expected_response: str = "OK", 
                       timeout: Optional[float] = None) -> Tuple[bool, List[str]]:
        """
        Send an AT command to the modem.
        
        Args:
            command: AT command to send
            expected_response: Expected response pattern
            timeout: Command timeout override
            
        Returns:
            Tuple of (success, response_lines)
        """
        pass

    @abstractmethod
    def get_signal_quality(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Get signal quality information.
        
        Returns:
            Tuple of (success, signal_info_dict)
        """
        pass

    @abstractmethod
    def get_network_operator(self) -> Tuple[bool, str]:
        """
        Get network operator information.
        
        Returns:
            Tuple of (success, operator_name)
        """
        pass

    @abstractmethod
    def get_phone_number(self) -> Tuple[bool, str]:
        """
        Get modem phone number.
        
        Returns:
            Tuple of (success, phone_number)
        """
        pass

    @abstractmethod
    def get_imei(self) -> Tuple[bool, str]:
        """
        Get modem IMEI.
        
        Returns:
            Tuple of (success, imei)
        """
        pass

    @abstractmethod
    def get_model(self) -> Tuple[bool, str]:
        """
        Get modem model information.
        
        Returns:
            Tuple of (success, model_info)
        """
        pass

    @abstractmethod
    def get_sim_status(self) -> Tuple[bool, str]:
        """
        Get SIM card status.
        
        Returns:
            Tuple of (success, sim_status)
        """
        pass

    @abstractmethod
    def set_sms_text_mode(self) -> bool:
        """
        Set SMS to text mode.
        
        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    def configure_sms_notifications(self, mode: int = 2, mt: int = 1) -> bool:
        """
        Configure SMS notification settings (CNMI).
        
        Args:
            mode: CNMI mode parameter
            mt: CNMI mt parameter
            
        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    def read_sms_messages(self) -> List[Dict[str, Any]]:
        """
        Read SMS messages from the modem.
        
        Returns:
            List of SMS message dictionaries
        """
        pass

    @abstractmethod
    def send_sms(self, phone_number: str, message: str) -> bool:
        """
        Send an SMS message.
        
        Args:
            phone_number: Destination phone number
            message: Message text to send
            
        Returns:
            True if successful, False otherwise
        """
        pass

    def update_info(self) -> bool:
        """
        Update modem information by querying the device.
        
        Returns:
            True if update successful, False otherwise
        """
        try:
            with self.lock:
                if not self.is_connected:
                    logger.warning(f"Cannot update info for {self.port}: not connected")
                    return False

                # Update basic info
                success, sim_status = self.get_sim_status()
                if success:
                    self._info.sim_status = sim_status

                success, signal_info = self.get_signal_quality()
                if success and isinstance(signal_info, dict):
                    self._info.signal_rssi = str(signal_info.get("rssi", "N/A"))
                    self._info.signal_ber = str(signal_info.get("ber", "N/A"))

                success, operator = self.get_network_operator()
                if success:
                    self._info.operator = operator

                success, phone_number = self.get_phone_number()
                if success:
                    self._info.phone_number = phone_number

                success, imei = self.get_imei()
                if success:
                    self._info.imei = imei

                success, model = self.get_model()
                if success:
                    self._info.model = model

                self._info.status = "RESPONSIVE" if self.is_connected else "DISCONNECTED"
                return True

        except Exception as e:
            logger.error(f"Error updating modem info for {self.port}: {e}", exc_info=True)
            self._last_error = str(e)
            return False

    def is_ready_for_sms(self) -> bool:
        """
        Check if modem is ready for SMS operations.
        
        Returns:
            True if ready, False otherwise
        """
        return (self.is_connected and 
                self._info.sim_status == "READY" and
                self._info.phone_number not in ["N/A", "UNKNOWN", "ERROR"] and
                len(self._info.phone_number) > 5)

    def __str__(self) -> str:
        """String representation of the modem."""
        return f"{self.__class__.__name__}(port={self.port}, status={self._info.status})"

    def __repr__(self) -> str:
        """Detailed string representation of the modem."""
        return (f"{self.__class__.__name__}(port={self.port}, "
                f"status={self._info.status}, model={self._info.model}, "
                f"phone={self._info.phone_number})")
