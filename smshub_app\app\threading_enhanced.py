"""
Enhanced threading module for the SMSHub application.

This module provides deadlock detection, thread monitoring,
and advanced synchronization primitives.
"""

import logging
import threading
import time
import weakref
from typing import Dict, List, Set, Optional, Any, Callable
from dataclasses import dataclass
from collections import defaultdict, deque
from contextlib import contextmanager

try:
    from .logging_enhanced import get_logger
    from .error_handling import report_error, ErrorCategory, ErrorSeverity
except ImportError:
    from logging_enhanced import get_logger
    from error_handling import report_error, ErrorCategory, ErrorSeverity

logger = get_logger(__name__, "ThreadingEnhanced")


@dataclass
class LockInfo:
    """Information about a lock."""
    lock_id: int
    lock_type: str
    acquired_by: Optional[int]  # thread ID
    acquired_at: Optional[float]
    waiting_threads: Set[int]
    name: Optional[str] = None


@dataclass
class ThreadInfo:
    """Information about a thread."""
    thread_id: int
    thread_name: str
    locks_held: Set[int]
    locks_waiting: Set[int]
    created_at: float
    last_activity: float


class DeadlockDetector:
    """
    Deadlock detection system using wait-for graph analysis.
    
    Monitors lock acquisitions and detects potential deadlocks
    by analyzing the wait-for graph between threads and locks.
    """

    def __init__(self, check_interval: float = 5.0):
        """
        Initialize the deadlock detector.
        
        Args:
            check_interval: How often to check for deadlocks (seconds)
        """
        self.check_interval = check_interval
        self._locks: Dict[int, LockInfo] = {}
        self._threads: Dict[int, ThreadInfo] = {}
        self._lock = threading.RLock()
        self._running = False
        self._detector_thread: Optional[threading.Thread] = None
        self._deadlock_callbacks: List[Callable] = []

    def start(self) -> None:
        """Start the deadlock detection system."""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            self._detector_thread = threading.Thread(
                target=self._detection_loop,
                daemon=True,
                name="DeadlockDetector"
            )
            self._detector_thread.start()
            logger.info("Deadlock detector started")

    def stop(self) -> None:
        """Stop the deadlock detection system."""
        with self._lock:
            self._running = False
            if self._detector_thread:
                self._detector_thread.join(timeout=5)
            logger.info("Deadlock detector stopped")

    def register_lock(self, lock: threading.Lock, name: str = None) -> None:
        """
        Register a lock for monitoring.
        
        Args:
            lock: Lock object to monitor
            name: Optional name for the lock
        """
        lock_id = id(lock)
        with self._lock:
            if lock_id not in self._locks:
                self._locks[lock_id] = LockInfo(
                    lock_id=lock_id,
                    lock_type=type(lock).__name__,
                    acquired_by=None,
                    acquired_at=None,
                    waiting_threads=set(),
                    name=name
                )
                logger.debug(f"Registered lock {name or lock_id} for monitoring")

    def on_lock_acquire_attempt(self, lock: threading.Lock) -> None:
        """
        Called when a thread attempts to acquire a lock.
        
        Args:
            lock: Lock being acquired
        """
        lock_id = id(lock)
        thread_id = threading.get_ident()
        
        with self._lock:
            # Register lock if not already registered
            if lock_id not in self._locks:
                self.register_lock(lock)
            
            # Register thread if not already registered
            if thread_id not in self._threads:
                self._register_thread(thread_id)
            
            # Add to waiting list if lock is held by another thread
            lock_info = self._locks[lock_id]
            if lock_info.acquired_by and lock_info.acquired_by != thread_id:
                lock_info.waiting_threads.add(thread_id)
                self._threads[thread_id].locks_waiting.add(lock_id)
                logger.debug(f"Thread {thread_id} waiting for lock {lock_id}")

    def on_lock_acquired(self, lock: threading.Lock) -> None:
        """
        Called when a thread successfully acquires a lock.
        
        Args:
            lock: Lock that was acquired
        """
        lock_id = id(lock)
        thread_id = threading.get_ident()
        
        with self._lock:
            if lock_id in self._locks:
                lock_info = self._locks[lock_id]
                lock_info.acquired_by = thread_id
                lock_info.acquired_at = time.time()
                lock_info.waiting_threads.discard(thread_id)
                
                if thread_id in self._threads:
                    thread_info = self._threads[thread_id]
                    thread_info.locks_held.add(lock_id)
                    thread_info.locks_waiting.discard(lock_id)
                    thread_info.last_activity = time.time()

    def on_lock_released(self, lock: threading.Lock) -> None:
        """
        Called when a thread releases a lock.
        
        Args:
            lock: Lock that was released
        """
        lock_id = id(lock)
        thread_id = threading.get_ident()
        
        with self._lock:
            if lock_id in self._locks:
                lock_info = self._locks[lock_id]
                if lock_info.acquired_by == thread_id:
                    lock_info.acquired_by = None
                    lock_info.acquired_at = None
                
                if thread_id in self._threads:
                    self._threads[thread_id].locks_held.discard(lock_id)

    def _register_thread(self, thread_id: int) -> None:
        """Register a thread for monitoring."""
        thread = threading.main_thread()
        for t in threading.enumerate():
            if t.ident == thread_id:
                thread = t
                break
        
        self._threads[thread_id] = ThreadInfo(
            thread_id=thread_id,
            thread_name=thread.name,
            locks_held=set(),
            locks_waiting=set(),
            created_at=time.time(),
            last_activity=time.time()
        )

    def _detection_loop(self) -> None:
        """Main detection loop."""
        while self._running:
            try:
                time.sleep(self.check_interval)
                if self._running:
                    self._check_for_deadlocks()
            except Exception as e:
                logger.error(f"Error in deadlock detection loop: {e}", exc_info=True)

    def _check_for_deadlocks(self) -> None:
        """Check for deadlocks using cycle detection in wait-for graph."""
        with self._lock:
            # Build wait-for graph
            wait_for_graph = self._build_wait_for_graph()
            
            # Detect cycles
            cycles = self._detect_cycles(wait_for_graph)
            
            if cycles:
                for cycle in cycles:
                    self._handle_deadlock(cycle)

    def _build_wait_for_graph(self) -> Dict[int, Set[int]]:
        """
        Build wait-for graph where edges represent thread dependencies.
        
        Returns:
            Graph where keys are thread IDs and values are sets of thread IDs they're waiting for
        """
        graph = defaultdict(set)
        
        for lock_id, lock_info in self._locks.items():
            if lock_info.acquired_by:
                # Threads waiting for this lock are waiting for the thread that holds it
                for waiting_thread in lock_info.waiting_threads:
                    graph[waiting_thread].add(lock_info.acquired_by)
        
        return dict(graph)

    def _detect_cycles(self, graph: Dict[int, Set[int]]) -> List[List[int]]:
        """
        Detect cycles in the wait-for graph using DFS.
        
        Args:
            graph: Wait-for graph
            
        Returns:
            List of cycles (each cycle is a list of thread IDs)
        """
        cycles = []
        visited = set()
        rec_stack = set()
        path = []
        
        def dfs(node: int) -> bool:
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True
            
            if node in visited:
                return False
            
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in graph.get(node, set()):
                if dfs(neighbor):
                    return True
            
            rec_stack.remove(node)
            path.pop()
            return False
        
        for node in graph:
            if node not in visited:
                dfs(node)
        
        return cycles

    def _handle_deadlock(self, cycle: List[int]) -> None:
        """
        Handle a detected deadlock.
        
        Args:
            cycle: List of thread IDs involved in the deadlock
        """
        thread_names = []
        for thread_id in cycle:
            thread_info = self._threads.get(thread_id)
            if thread_info:
                thread_names.append(f"{thread_info.thread_name}({thread_id})")
            else:
                thread_names.append(str(thread_id))
        
        deadlock_info = {
            "cycle": cycle,
            "thread_names": thread_names,
            "detected_at": time.time()
        }
        
        logger.critical(f"DEADLOCK DETECTED: {' -> '.join(thread_names)}")
        
        # Report error
        report_error(
            f"Deadlock detected involving threads: {', '.join(thread_names)}",
            "DeadlockDetector",
            ErrorCategory.UNKNOWN,
            ErrorSeverity.CRITICAL,
            context=deadlock_info
        )
        
        # Call registered callbacks
        for callback in self._deadlock_callbacks:
            try:
                callback(deadlock_info)
            except Exception as e:
                logger.error(f"Error in deadlock callback: {e}")

    def add_deadlock_callback(self, callback: Callable) -> None:
        """
        Add a callback to be called when deadlocks are detected.
        
        Args:
            callback: Function to call with deadlock information
        """
        self._deadlock_callbacks.append(callback)

    def get_stats(self) -> Dict[str, Any]:
        """Get deadlock detector statistics."""
        with self._lock:
            return {
                "running": self._running,
                "monitored_locks": len(self._locks),
                "monitored_threads": len(self._threads),
                "check_interval": self.check_interval,
                "locks": {
                    lock_id: {
                        "type": info.lock_type,
                        "name": info.name,
                        "acquired_by": info.acquired_by,
                        "waiting_threads": len(info.waiting_threads)
                    }
                    for lock_id, info in self._locks.items()
                },
                "threads": {
                    thread_id: {
                        "name": info.thread_name,
                        "locks_held": len(info.locks_held),
                        "locks_waiting": len(info.locks_waiting)
                    }
                    for thread_id, info in self._threads.items()
                }
            }


class MonitoredLock:
    """
    A lock wrapper that automatically reports to the deadlock detector.
    """

    def __init__(self, lock: threading.Lock = None, name: str = None, detector: DeadlockDetector = None):
        """
        Initialize monitored lock.
        
        Args:
            lock: Underlying lock (creates RLock if None)
            name: Name for the lock
            detector: Deadlock detector to use
        """
        self._lock = lock or threading.RLock()
        self._name = name
        self._detector = detector or get_deadlock_detector()
        
        # Register with detector
        self._detector.register_lock(self._lock, name)

    def acquire(self, blocking: bool = True, timeout: float = -1) -> bool:
        """Acquire the lock with monitoring."""
        self._detector.on_lock_acquire_attempt(self._lock)
        
        try:
            if timeout == -1:
                result = self._lock.acquire(blocking)
            else:
                result = self._lock.acquire(blocking, timeout)
            
            if result:
                self._detector.on_lock_acquired(self._lock)
            
            return result
        except Exception as e:
            logger.error(f"Error acquiring lock {self._name}: {e}")
            raise

    def release(self) -> None:
        """Release the lock with monitoring."""
        try:
            self._lock.release()
            self._detector.on_lock_released(self._lock)
        except Exception as e:
            logger.error(f"Error releasing lock {self._name}: {e}")
            raise

    def __enter__(self):
        """Context manager entry."""
        self.acquire()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.release()


# Global deadlock detector
_deadlock_detector: Optional[DeadlockDetector] = None


def get_deadlock_detector() -> DeadlockDetector:
    """Get the global deadlock detector."""
    global _deadlock_detector
    if _deadlock_detector is None:
        _deadlock_detector = DeadlockDetector()
        _deadlock_detector.start()
    return _deadlock_detector


def create_monitored_lock(name: str = None) -> MonitoredLock:
    """
    Create a monitored lock.
    
    Args:
        name: Optional name for the lock
        
    Returns:
        MonitoredLock instance
    """
    return MonitoredLock(name=name)


@contextmanager
def monitored_lock_context(name: str = None):
    """
    Context manager for temporary monitored locks.
    
    Args:
        name: Optional name for the lock
    """
    lock = create_monitored_lock(name)
    with lock:
        yield lock


def shutdown_threading_monitor() -> None:
    """Shutdown the threading monitoring system."""
    global _deadlock_detector
    if _deadlock_detector:
        _deadlock_detector.stop()
        _deadlock_detector = None
