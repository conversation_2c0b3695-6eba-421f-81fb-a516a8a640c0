import glob
import logging
import time
import threading 
from typing import Any, Dict, List, Optional 
from datetime import datetime 

try:
    import serial
    import serial.tools.list_ports
except ImportError:
    logging.warning("pyserial library not found. Modem communication will not be available.")
    serial = None

from .config_manager import load_config
from .database_manager import add_message 

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG) # Ensure this logger is set to DEBUG

active_modem_readers: Dict[str, Dict[str, Any]] = {}

class ModemCommunicator:
    def __init__(self, port: str, baudrate: int = 115200, timeout: float = 1.0):
        if serial is None:
            raise ImportError("pyserial library is not installed. Modem communication is not possible.")
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.ser = serial.Serial() 
        self.lock = threading.Lock()

    def connect(self):
        with self.lock:
            if self.ser.is_open:
                logger.debug(f"Port {self.port} is already open.")
                return True
            try:
                self.ser.port = self.port
                self.ser.baudrate = self.baudrate
                self.ser.timeout = self.timeout 
                self.ser.write_timeout = self.timeout 
                self.ser.open()
                logger.info(f"Successfully connected to modem on port {self.port} at {self.baudrate} baud.")
                return True
            except Exception as e:
                logger.error(f"Failed to open serial port {self.port}: {e}")
                return False

    def disconnect(self):
        with self.lock:
            if self.ser.is_open:
                try:
                    self.ser.close()
                    logger.info(f"Disconnected from modem on port {self.port}.")
                except Exception as e:
                    logger.error(f"Error closing serial port {self.port}: {e}")
            else:
                logger.debug(f"Port {self.port} was already closed.")

    def send_at_command(self, command: str, expected_response: str = "OK", timeout_override: float | None = None):
        with self.lock:
            if not self.ser.is_open:
                logger.error(f"Port {self.port} is not open. Command '{command}' not sent.")
                return False, ["ERROR: Port not open"]

            full_command = command + '\r\n'
            response_lines = []
            success = False
            original_read_timeout = self.ser.timeout
            original_write_timeout = self.ser.write_timeout
            current_read_timeout = timeout_override if timeout_override is not None else self.timeout
            current_write_timeout = timeout_override if timeout_override is not None else self.timeout 
            self.ser.timeout = current_read_timeout
            self.ser.write_timeout = current_write_timeout
            
            try:
                self.ser.reset_input_buffer()
                self.ser.reset_output_buffer()
                logger.debug(f"Sending command to {self.port}: {command!r} (RTO:{current_read_timeout}, WTO:{current_write_timeout})")
                self.ser.write(full_command.encode('utf-8'))
                start_time = time.time()
                while True:
                    if time.time() - start_time > current_read_timeout:
                        logger.warning(f"Timeout waiting for response to '{command}' on {self.port}.")
                        response_lines.append("ERROR: TIMEOUT")
                        break
                    line_bytes = self.ser.readline()
                    if not line_bytes:
                        if time.time() - start_time > current_read_timeout:
                            if not response_lines: response_lines.append("ERROR: TIMEOUT (readline)")
                            break
                        continue
                    try: line = line_bytes.decode('utf-8').strip()
                    except UnicodeDecodeError: line = line_bytes.decode('latin-1').strip(); logger.warning(f"UnicodeDecodeError on {self.port} for command '{command}', used latin-1.")
                    logger.debug(f"Received from {self.port} for '{command}': {line!r}")
                    if line: response_lines.append(line)
                    if expected_response in line: success = True; break
                    if "ERROR" in line and expected_response != "ERROR": break
            except Exception as e:
                if serial and isinstance(e, serial.SerialTimeoutException):
                    logger.error(f"SerialTimeoutException on port {self.port} for command '{command}': {e}")
                    response_lines.append(f"ERROR: SERIAL TIMEOUT EXCEPTION - {e}")
                elif serial and isinstance(e, serial.SerialException):
                    logger.error(f"SerialException on port {self.port} for command '{command}': {e}")
                    response_lines.append(f"ERROR: SERIAL EXCEPTION - {e}")
                else:
                    logger.error(f"Unexpected error on port {self.port} for command '{command}': {e}", exc_info=True)
                    response_lines.append(f"ERROR: UNEXPECTED - {e}")
                success = False
            finally:
                self.ser.timeout = original_read_timeout
                self.ser.write_timeout = original_write_timeout
            logger.debug(f"Command '{command}' on {self.port} - Success: {success}, Response: {response_lines}")
            return success, response_lines

def _execute_modem_command(communicator: ModemCommunicator, func_name: str, at_command: str, expected_in_response: str, parse_logic, timeout_override: float):
    if not isinstance(communicator, ModemCommunicator): return False, "ERROR: Invalid communicator"
    success, response = communicator.send_at_command(at_command, expected_response=expected_in_response, timeout_override=timeout_override)
    
    if func_name == "get_phone_number":
        logger.info(f"Raw response for {at_command} on {communicator.port} (func: {func_name}): {response}")

    if success:
        return parse_logic(response, communicator.port)
    logger.debug(f"{func_name} on {communicator.port}: Command '{at_command}' failed. Expected '{expected_in_response}'. Response: {' '.join(response)}")
    if any("ERROR" in line for line in response): return False, "ERROR: Modem reported error"
    return False, "ERROR: No valid response or expected string not found"

def check_modem_connection(communicator: ModemCommunicator):
    success, _ = _execute_modem_command(communicator, "check_modem_connection", "AT", "OK", lambda r,p: (True, "OK"), 1.0)
    return success

def get_sim_status(communicator: ModemCommunicator):
    def parse(response, port):
        for line in response:
            if line.startswith("+CPIN:"): return True, line.replace("+CPIN:", "").strip()
        return False, "UNKNOWN: Format error"
    return _execute_modem_command(communicator, "get_sim_status", "AT+CPIN?", "+CPIN:", parse, 2.0)

def get_signal_quality(communicator: ModemCommunicator):
    def parse(response, port):
        for line in response:
            if line.startswith("+CSQ:"):
                parts = line.replace("+CSQ:", "").strip().split(',')
                if len(parts) == 2:
                    try: return True, {"rssi": int(parts[0]), "ber": int(parts[1])}
                    except ValueError: return False, {"error": "Format error"}
        return False, {"error": "Parse error"}
    return _execute_modem_command(communicator, "get_signal_quality", "AT+CSQ", "+CSQ:", parse, 2.0)

def get_network_operator(communicator: ModemCommunicator):
    def parse(response, port):
        for line in response:
            if line.startswith("+COPS:"):
                parts = line[len("+COPS:"):].strip().split(',')
                if not parts or not parts[0]: return False, "UNKNOWN: Malformed +COPS"
                mode = parts[0].strip()
                if mode == "2": return True, "Not Registered"
                if mode in ["0", "1"] and len(parts) >= 3:
                    op_name = parts[2].strip().replace('"', '')
                    return True, op_name if op_name else "Registered (No Name)"
                return True, f"Unknown (Mode {mode})"
        return False, "UNKNOWN: +COPS line not found"
    return _execute_modem_command(communicator, "get_network_operator", "AT+COPS?", "+COPS:", parse, 3.0)

def get_phone_number(communicator: ModemCommunicator):
    def parse(response, port):
        for line in response:
            if line.startswith("+CNUM:"):
                parts = line.split(','); num_part = parts[1].strip().replace('"', '') if len(parts) > 1 else None
                return True, num_part if num_part else "NOT AVAILABLE"
        return False, "UNKNOWN: Format error"
    return _execute_modem_command(communicator, "get_phone_number", "AT+CNUM", "+CNUM:", parse, 3.0)

def get_imei(communicator: ModemCommunicator):
    def parse(response, port):
        for line in response: 
            stripped_line = line.strip()
            if stripped_line.isdigit() and 14 <= len(stripped_line) <= 16: return True, stripped_line
            if stripped_line.startswith('+CGSN:'): 
                imei_val = stripped_line.split(':', 1)[1].strip().replace('"', '')
                if imei_val.isdigit() and 14 <= len(imei_val) <= 16: return True, imei_val
        return False, "UNKNOWN: Format error"
    return _execute_modem_command(communicator, "get_imei", "AT+CGSN", "OK", parse, 2.0)

def get_modem_model(communicator: ModemCommunicator):
    def parse(response, port):
        for line in response:
            stripped_line = line.strip()
            if stripped_line.startswith("+CGMM:"): return True, stripped_line.replace("+CGMM:", "").strip().replace('"', '')
            if stripped_line and stripped_line != "OK" and not stripped_line.upper().startswith("AT+CGMM"): return True, stripped_line
        return False, "UNKNOWN: Format error"
    return _execute_modem_command(communicator, "get_modem_model", "AT+CGMM", "OK", parse, 2.0)

def set_sms_text_mode(communicator: ModemCommunicator):
    if not isinstance(communicator, ModemCommunicator): return False
    success, _ = communicator.send_at_command("AT+CMGF=1", expected_response="OK", timeout_override=2.0)
    if success: logger.info(f"Port {communicator.port}: Successfully set SMS to text mode (AT+CMGF=1).")
    else: logger.error(f"Port {communicator.port}: Failed to set SMS to text mode.")
    return success

def set_new_message_indications(communicator: ModemCommunicator, mode: int = 2, mt: int = 1, bm: int = 0, ds: int = 0, bfr: int = 0):
    if not isinstance(communicator, ModemCommunicator): return False
    cnmi_command = f"AT+CNMI={mode},{mt},{bm},{ds},{bfr}"
    success, response = communicator.send_at_command(cnmi_command, expected_response="OK", timeout_override=2.0)
    if success: logger.info(f"Port {communicator.port}: Successfully set new message indications with {cnmi_command}.")
    else: logger.error(f"Port {communicator.port}: Failed to set new message indications with {cnmi_command}. Response: {response}")
    return success

def get_cnmi_settings(communicator: ModemCommunicator):
    if not isinstance(communicator, ModemCommunicator):
        # Attempt to get port for logging if possible, otherwise 'N/A'
        port_for_log = 'N/A'
        if hasattr(communicator, 'port'):
            port_for_log = communicator.port
        elif isinstance(communicator, dict) and 'port' in communicator: # Should not happen with type hints, but defensive
            port_for_log = communicator['port']
        logger.error(f"Invalid communicator passed to get_cnmi_settings for port {port_for_log}")
        return False, "ERROR: Invalid communicator"
    
    success, response = communicator.send_at_command("AT+CNMI?", expected_response="+CNMI:", timeout_override=3.0)
    if success:
        cnmi_values = "Unknown"
        for line in response:
            if line.startswith("+CNMI:"):
                cnmi_values = line.replace("+CNMI:", "").strip()
                logger.info(f"Port {communicator.port}: Queried CNMI settings: {cnmi_values}")
                return True, cnmi_values
        logger.warning(f"Port {communicator.port}: AT+CNMI? command succeeded but +CNMI line not found in response: {response}")
        return False, "ERROR: +CNMI line not found in response"
    else:
        logger.error(f"Port {communicator.port}: Failed to query CNMI settings (AT+CNMI?). Response: {response}")
        return False, "ERROR: Failed to query CNMI"

def list_sms(communicator: ModemCommunicator) -> List[Dict[str, Any]]:
    if not isinstance(communicator, ModemCommunicator):
        logger.error("Invalid communicator passed to list_sms"); return []
    list_commands_to_try = ['AT+CMGL="ALL"', 'AT+CMGL=4', 'AT+CMGL="STO ALL"']
    messages = []
    response_lines_final = []
    list_success = False
    for cmd_idx, at_command in enumerate(list_commands_to_try):
        logger.debug(f"Port {communicator.port}: Attempting list_sms with command: {at_command}")
        current_cmd_success, current_response_lines = communicator.send_at_command(at_command, expected_response="OK", timeout_override=15.0)
        if current_cmd_success:
            if any(line.startswith("+CMGL:") for line in current_response_lines) or \
               ("OK" in current_response_lines and not any(l.startswith("+CMGL:") for l in current_response_lines)):
                list_success = True; response_lines_final = current_response_lines
                logger.info(f"Port {communicator.port}: Successfully used {at_command} for listing SMS.")
                break
        if cmd_idx == len(list_commands_to_try) - 1 and not list_success:
            logger.error(f"Port {communicator.port}: All AT+CMGL variants failed. Last response: {' '.join(current_response_lines)}")
            return []
    if not list_success: return [] 
    current_message_info: dict[str, Any] = {}
    for line in response_lines_final:
        if line.startswith("+CMGL:"):
            if current_message_info.get("index") is not None and "message_lines" in current_message_info:
                current_message_info["message"] = "\n".join(current_message_info["message_lines"]).strip()
                messages.append(current_message_info)
            current_message_info = {}
            try:
                payload = line[len("+CMGL: "):]
                parts = payload.split(',', 4) 
                if len(parts) < 3: raise ValueError("Not enough parts for +CMGL basic info")
                current_message_info["index"] = int(parts[0].strip())
                current_message_info["status"] = parts[1].strip().replace('"', '')
                current_message_info["sender"] = parts[2].strip().replace('"', '')
                current_message_info["alpha"] = parts[3].strip().replace('"', '') if len(parts) > 3 else ""
                current_message_info["timestamp"] = parts[4].strip().replace('"', '') if len(parts) > 4 else ""
                current_message_info["message_lines"] = []
            except (ValueError, IndexError) as e:
                logger.error(f"Port {communicator.port}: Error parsing +CMGL line '{line}': {e}")
                current_message_info = {} 
        elif "index" in current_message_info and "message_lines" in current_message_info:
            current_message_info["message_lines"].append(line)
    if current_message_info.get("index") is not None and "message_lines" in current_message_info:
        current_message_info["message"] = "\n".join(current_message_info["message_lines"]).strip()
        messages.append(current_message_info)
    logger.info(f"Port {communicator.port}: Found {len(messages)} SMS messages after parsing AT+CMGL.")
    return messages

def read_sms(communicator: ModemCommunicator, index: int) -> Optional[Dict[str, Any]]:
    if not isinstance(communicator, ModemCommunicator): logger.error("Invalid communicator for read_sms"); return None
    at_command = f'AT+CMGR={index}'
    success, response_lines = communicator.send_at_command(at_command, expected_response="OK", timeout_override=5.0)
    if not success or not response_lines:
        logger.error(f"Port {communicator.port}: {at_command} failed. Response: {' '.join(response_lines if response_lines else [])}")
        return None
    message_info: dict[str, Any] = {"index": index}
    message_body_lines = []
    header_line_parsed = False
    for line in response_lines:
        if line.startswith("+CMGR:"):
            if header_line_parsed: logger.warning(f"Port {communicator.port}: Multiple +CMGR lines for index {index}."); continue
            payload = line[len("+CMGR: "):]
            try:
                parts = payload.split(',', 3) 
                if len(parts) < 2: raise ValueError("Not enough parts for +CMGR basic info")
                message_info["status"] = parts[0].strip().replace('"', '')
                message_info["sender"] = parts[1].strip().replace('"', '')
                message_info["alpha"] = ""
                message_info["timestamp"] = ""
                if len(parts) > 2: 
                    alpha_scts_payload = parts[2].strip()
                    alpha_scts_parts = alpha_scts_payload.split(',', 1)
                    message_info["alpha"] = alpha_scts_parts[0].strip().replace('"', '')
                    if len(alpha_scts_parts) > 1:
                        message_info["timestamp"] = alpha_scts_parts[1].strip().replace('"', '')
                header_line_parsed = True
            except (ValueError, IndexError) as e:
                logger.error(f"Port {communicator.port}: Error parsing +CMGR line '{line}' for index {index}: {e}"); return None
        elif header_line_parsed and line != "OK":
            message_body_lines.append(line)
    if not header_line_parsed:
        logger.warning(f"Port {communicator.port}: +CMGR line not found for index {index}. Response: {' '.join(response_lines)}"); return None
    message_info["message"] = "\n".join(message_body_lines).strip()
    logger.info(f"Port {communicator.port}: Successfully read SMS at index {index}.")
    return message_info

def delete_sms(communicator: ModemCommunicator, index: int, del_flag: int = 0):
    if not isinstance(communicator, ModemCommunicator): logger.error("Invalid communicator for delete_sms"); return False
    at_command = f'AT+CMGD={index},{del_flag}'
    success, response = communicator.send_at_command(at_command, expected_response="OK", timeout_override=5.0)
    if success: logger.info(f"Port {communicator.port}: Successfully deleted SMS at index {index} (flag={del_flag}).")
    else: logger.error(f"Port {communicator.port}: Failed to delete SMS at index {index} (flag={del_flag}). Response: {' '.join(response)}")
    return success

def parse_cmt_message(lines: List[str], port: str) -> Optional[Dict[str, Any]]:
    if not lines or not lines[0].startswith("+CMT:"): return None
    try:
        header_line = lines[0]
        message_body_lines = lines[1:]
        parts_str = header_line[len("+CMT: "):].strip()
        parts = []
        in_quote = False
        current_part = ""
        for char in parts_str:
            if char == '"': in_quote = not in_quote
            if char == ',' and not in_quote: parts.append(current_part); current_part = ""
            else: current_part += char
        parts.append(current_part)
        sender = parts[0].strip().replace('"', '')
        timestamp_device = parts[2].strip().replace('"', '') if len(parts) > 2 else datetime.now().strftime("%y/%m/%d,%H:%M:%S+00")
        message_body = "\n".join(message_body_lines).strip()
        if not sender or not message_body:
            logger.warning(f"CMT Parse Warning on {port}: Missing sender or body. Header: '{header_line}', Body: '{message_body}'")
            return None
        logger.info(f"Parsed +CMT on {port}: From {sender}, Time {timestamp_device}, Body: {message_body[:30]}...")
        return {"modem_port": port, "sender": sender, "timestamp_device": timestamp_device,
                "timestamp_received_app": datetime.now().isoformat(), "message_body": message_body,
                "status_on_modem": "REC UNREAD VIA CMT", "modem_message_index": None}
    except Exception as e:
        logger.error(f"Error parsing +CMT message on {port}: {e}. Lines: {lines}", exc_info=True)
        return None

def modem_reader_thread_func(port: str, communicator: ModemCommunicator, stop_event: threading.Event):
    logger.info(f"Reader thread started for modem {port}.")
    cmt_buffer: List[str] = []
    is_reading_cmt_body = False

    # Define known URCs that are not part of a CMT message body and can interrupt it.
    # These will trigger finalization of any ongoing CMT message.
    KNOWN_INTERRUPTING_URCS = (
        "+CDS:", "+CBM:", "RING", "+CLIP:", "+CCWA:", "+CSQN:", "+CREG:", "+CGREG:", "+CTZV:", # Common GSM/3GPP URCs
        "NO CARRIER", "BUSY", "NO ANSWER", "NO DIALTONE", "CONNECT", # Call-related responses/URCs
        "ERROR", # General error, might terminate an operation or appear unsolicited
        # Modem-specific URCs (examples, add more if known for target devices)
        "^RSSI:", "^MODE:", "^SYSINFO:", "^SRVST:", "^CONN:", "^BOOT:",
        "+PBREADY", # SIM Phonebook ready
        # Add other URCs that might appear and are not part of SMS message text
    )

    logger.debug(f"Reader for {port}: Initializing thread function. stop_event initially set: {stop_event.is_set()}. Communicator is open: {communicator.ser.is_open if communicator and hasattr(communicator, 'ser') else 'N/A'}")
    while not stop_event.is_set():
        logger.debug(f"Reader for {port}: Top of main loop. stop_event set: {stop_event.is_set()}, cmt_buffer: {cmt_buffer}, is_reading_cmt_body: {is_reading_cmt_body}")
        try:
            if not communicator.ser.is_open:
                logger.warning(f"Reader thread for {port}: Serial port not open. Attempting to reconnect...")
                if communicator.connect():
                    logger.info(f"Reader thread for {port}: Reconnected.")
                else:
                    logger.error(f"Reader thread for {port}: Reconnect failed. Sleeping for 10s.")
                    stop_event.wait(10) # Use stop_event.wait for interruptible sleep
                    continue
            
            original_timeout = communicator.ser.timeout
            communicator.ser.timeout = 0.5 # Short timeout for non-blocking read attempt
            logger.debug(f"Reader for {port}: Attempting readline() with timeout 0.5s")
            line_bytes = communicator.ser.readline()
            communicator.ser.timeout = original_timeout # Restore original timeout

            if not line_bytes:  # readline() timed out
                logger.debug(f"Reader for {port}: readline() timed out (returned empty bytes).")
                # If we were in the middle of reading a CMT message, the timeout means the message is complete.
                if is_reading_cmt_body and cmt_buffer:
                    logger.info(f"Reader for {port}: Readline timeout. Current state: cmt_buffer={cmt_buffer}, is_reading_cmt_body={is_reading_cmt_body}")
                    if is_reading_cmt_body and cmt_buffer:
                        logger.info(f"Reader for {port}: Timeout occurred while reading CMT. Finalizing. Buffer: {cmt_buffer}")
                        parsed_message = parse_cmt_message(list(cmt_buffer), port) # Pass a copy
                        logger.info(f"Reader for {port}: Parsed CMT (after timeout): {parsed_message}")
                        if parsed_message:
                            logger.info(f"Reader for {port}: Attempting to add parsed message (after timeout) to DB: Sender {parsed_message.get('sender')}")
                            db_id = add_message(parsed_message)
                            logger.info(f"Reader for {port}: add_message (timed out CMT) result: DB ID {db_id if db_id is not None else 'None (duplicate or error)'}")
                        else:
                            logger.warning(f"Reader for {port}: CMT parsing (after timeout) resulted in None. Original buffer was: {cmt_buffer}")
                    elif cmt_buffer: # Buffer has content, but we weren't in CMT mode. This shouldn't happen if direct messages are processed immediately.
                         logger.warning(f"Reader for {port}: Timeout with non-empty buffer but not in CMT read mode. Buffer: {cmt_buffer}. Discarding.")
                    
                    cmt_buffer = [] # Always clear buffer and reset state on timeout
                    is_reading_cmt_body = False
                    logger.debug(f"Reader for {port}: Timeout processed. CMT state reset.")
                # Continue to next iteration whether CMT was processed or not
                # No 'time.sleep()' here as readline already waited for the timeout
                continue

            # If we reach here, line_bytes contains data
            logger.debug(f"Reader for {port}: readline() received {len(line_bytes)} bytes.")
            try:
                line = line_bytes.decode('utf-8').strip()
                logger.debug(f"Reader for {port} decoded line (utf-8): {line!r}")
            except UnicodeDecodeError:
                line = line_bytes.decode('latin-1', errors='ignore').strip() # Use ignore for safety
                logger.warning(f"Reader thread for {port}: UnicodeDecodeError, used latin-1 (errors ignored) for line: {line!r}")

            # Log current state BEFORE processing the received line for better diagnostics
            logger.info(f"Reader for {port}: Processing line: {line!r}. PRE-State: cmt_buffer={cmt_buffer}, is_reading_cmt_body={is_reading_cmt_body}")

            if line.startswith("+CMT:"):
                if is_reading_cmt_body and cmt_buffer:
                    logger.info(f"Reader for {port}: New +CMT detected. Finalizing previous CMT in buffer. Old Buffer: {cmt_buffer}")
                    parsed_old_message = parse_cmt_message(list(cmt_buffer), port) # Pass a copy
                    if parsed_old_message:
                        logger.info(f"Reader for {port}: Adding old CMT (due to new +CMT): Sender {parsed_old_message.get('sender')}")
                        add_message(parsed_old_message) # DB log inside this func
                    else:
                        logger.warning(f"Reader for {port}: Failed to parse old CMT (before new +CMT). Discarding. Buffer was: {cmt_buffer}")
                
                cmt_buffer = [line] # Start new CMT message
                is_reading_cmt_body = True
                logger.info(f"Reader for {port}: Started new +CMT message. Buffer set to: {[line]}, is_reading_cmt_body set to True.")

            elif any(line.startswith(urc_prefix) for urc_prefix in KNOWN_INTERRUPTING_URCS):
                if is_reading_cmt_body and cmt_buffer:
                    logger.info(f"Reader for {port}: Known URC '{line[:30]}' received. Finalizing current CMT. Buffer: {cmt_buffer}")
                    parsed_message = parse_cmt_message(list(cmt_buffer), port) # Pass a copy
                    if parsed_message:
                        logger.info(f"Reader for {port}: Adding interrupted CMT: Sender {parsed_message.get('sender')}")
                        add_message(parsed_message) # DB log inside this func
                    else:
                        logger.warning(f"Reader for {port}: Failed to parse interrupted CMT. Discarding. Buffer was: {cmt_buffer}")
                
                cmt_buffer = []
                is_reading_cmt_body = False
                logger.info(f"Reader for {port}: Processed known interrupting URC: {line!r}. CMT state reset (buffer cleared, is_reading_cmt_body=False).")

            elif is_reading_cmt_body:
                # This condition means: not +CMT, not other known URC, but we ARE expecting a CMT body.
                cmt_buffer.append(line)
                logger.info(f"Reader for {port}: Appended to active CMT body. Buffer now: {cmt_buffer}")

            elif line:
                # This condition means: not +CMT, not other known URC, NOT expecting a CMT body, AND line has content.
                # This is our path for direct SMS content.
                
                # Filter out common non-SMS responses
                stripped_line_upper = line.strip().upper()
                if stripped_line_upper == "OK" or \
                   stripped_line_upper == "ERROR" or \
                   stripped_line_upper.startswith("AT") or \
                   stripped_line_upper.startswith("^"): # Common modem status URCs often start with ^
                    logger.debug(f"Reader for {port}: Ignoring common non-SMS response/echo: {line!r}")
                else:
                    logger.info(f"Reader for {port}: Unsolicited non-CMT/URC line (treating as direct SMS): {line!r}")
                    direct_message_data = {
                        "modem_port": port, "sender": f"DIRECT_FROM_{port}", # Placeholder sender
                        "timestamp_device": datetime.now().strftime("%y/%m/%d,%H:%M:%S+00"), # App time as approximation
                        "timestamp_received_app": datetime.now().isoformat(),
                        "message_body": line.strip(),
                        "status_on_modem": "REC UNREAD DIRECT",
                        "modem_message_index": None # No index for URCs/direct messages
                    }
                    logger.info(f"Reader for {port}: Attempting to add direct message: Body '{direct_message_data.get('message_body', '')[:50]}...'")
                    db_id_direct = add_message(direct_message_data)
                    logger.info(f"Reader for {port}: add_message (direct) result: DB ID {db_id_direct if db_id_direct is not None else 'None (duplicate or error)'}")
                    # IMPORTANT: cmt_buffer and is_reading_cmt_body are NOT modified here.
            
            else: # Empty line, and not currently reading a CMT body.
                logger.debug(f"Reader for {port}: Received empty line, no action.")
        except Exception as e:
            if serial and isinstance(e, serial.SerialException):
                 logger.error(f"Reader thread for {port}: SerialException: {e}. Attempting to close and reopen.")
                 communicator.disconnect() 
                 stop_event.wait(5) 
            else:
                logger.error(f"Reader thread for {port}: Unexpected error: {e}", exc_info=True)
                stop_event.wait(5) 
    communicator.disconnect() 
    logger.info(f"Reader thread stopped for modem {port}.")

def get_modem_info(port: str, baudrate: int = 115200, timeout: float = 1.0):
    if serial is None: return {"port": port, "status": "ERROR: pyserial not installed"}
    comm = ModemCommunicator(port=port, baudrate=baudrate, timeout=timeout)
    info: dict[str, Any] = {"port": port, "status": "UNKNOWN", "connection_details": "N/A", "sim_status": "N/A",
            "signal_rssi": "N/A", "signal_ber": "N/A", "operator": "N/A",
            "phone_number": "N/A", "imei": "N/A", "model": "N/A"}
    try:
        if not comm.connect():
            info["status"] = "ERROR: Connection failed"; info["connection_details"] = f"Failed to open serial port {port}"; return info
        info["status"] = "CONNECTED"; info["connection_details"] = f"Connected at {baudrate} baud, timeout {timeout}s"
        if not check_modem_connection(comm): info["status"] = "CONNECTED_NOT_RESPONSIVE"; return info
        info["status"] = "RESPONSIVE"
        _, info["sim_status"] = get_sim_status(comm)
        sq_ok, sq_val = get_signal_quality(comm); 
        info["signal_rssi"] = str(sq_val.get("rssi","N/A")) if sq_ok and isinstance(sq_val, dict) else "ERROR"
        info["signal_ber"] = str(sq_val.get("ber","N/A")) if sq_ok and isinstance(sq_val, dict) else "ERROR"
        _, info["operator"] = get_network_operator(comm)
        _, info["phone_number"] = get_phone_number(comm)
        _, info["imei"] = get_imei(comm)
        _, info["model"] = get_modem_model(comm)
    except Exception as e:
        logger.error(f"Unexpected error in get_modem_info for {port}: {e}", exc_info=True)
        info["status"] = "ERROR: Exception during info gathering"
    finally:
        if 'comm' in locals(): comm.disconnect()
    logger.info(f"Modem info for {port}: {info}")
    return info

def detect_modems():
    if serial is None or not hasattr(serial, 'tools') or not hasattr(serial.tools, 'list_ports'):
        logger.warning("pyserial or serial.tools.list_ports not found. Cannot auto-detect modems.")
        return []
    config_data = load_config()
    app_config = config_data if config_data is not None else {}
    default_baudrate = app_config.get("default_modem_baudrate", 115200)
    default_timeout = float(app_config.get("default_modem_timeout", 1.0))
    all_detected_modem_details = []
    known_modems_vid_pid = [ 
        {"vid": 0x8087, "pid": 0x095A, "model_hint": "Fibocom L850-GL (Intel)"},
        {"vid": 0x413C, "pid": 0x81D9, "model_hint": "Fibocom L850-GL (Dell)"},
        {"vid": 0x1410, "pid": 0xB001, "model_hint": "Novatel USB551L"}
    ]
    try: system_ports = serial.tools.list_ports.comports()
    except Exception as e: logger.error(f"Error listing system serial ports: {e}"); return []
    if not system_ports: logger.info("No serial ports found."); return []
    logger.info(f"Found {len(system_ports)} system serial port(s): {[p.device for p in system_ports]}")
    for port_info in system_ports:
        port_device = port_info.device
        vid_str = f"{port_info.vid:#06x}" if port_info.vid is not None else "N/A" 
        pid_str = f"{port_info.pid:#06x}" if port_info.pid is not None else "N/A" 
        model_hint = next((k["model_hint"] for k in known_modems_vid_pid if port_info.vid == k["vid"] and port_info.pid == k["pid"]), "Unknown (Generic Serial)")
        is_known_modem = model_hint != "Unknown (Generic Serial)"
        log_prefix = f"Port '{port_device}' (VID:{vid_str}, PID:{pid_str}, Hint: {model_hint}):"
        temp_comm = None
        try:
            logger.debug(f"{log_prefix} Creating ModemCommunicator instance for probe.")
            temp_comm = ModemCommunicator(port=port_device, baudrate=default_baudrate, timeout=0.5)
            if not temp_comm.connect(): 
                logger.warning(f"{log_prefix} Failed to connect for initial probe."); continue
            success_at, _ = temp_comm.send_at_command("AT", timeout_override=1.0)
            success_ati = False
            if not success_at: success_ati, _ = temp_comm.send_at_command("ATI", timeout_override=1.5)
            is_responsive = success_at or success_ati
            temp_comm.disconnect() 
            if is_responsive:
                logger.info(f"{log_prefix} Responded to AT/ATI. Getting detailed info...")
                modem_details = get_modem_info(port=port_device, baudrate=default_baudrate, timeout=default_timeout)
                if modem_details and modem_details.get("status") == "RESPONSIVE":
                    modem_details.setdefault("modem_name_config", model_hint if is_known_modem else f"Auto-Detected on {port_device}")
                    modem_details["description"] = port_info.description; modem_details["hwid"] = port_info.hwid
                    modem_details["vid"] = vid_str; modem_details["pid"] = pid_str 
                    all_detected_modem_details.append(modem_details)
                elif modem_details:
                     logger.warning(f"{log_prefix} get_modem_info returned status {modem_details.get('status')}, adding as error entry.")
                     all_detected_modem_details.append(modem_details) 
                else: logger.warning(f"{log_prefix} get_modem_info returned None.")
            else: logger.debug(f"{log_prefix} Did not respond as expected to AT/ATI.")
        except Exception as e: logger.error(f"{log_prefix} Unexpected error during probing: {e}", exc_info=True)
        finally:
            if temp_comm and temp_comm.ser.is_open: temp_comm.disconnect()
    logger.info(f"Auto-detection complete. Found {len(all_detected_modem_details)} modem entries.")
    return all_detected_modem_details

_initialized_modems_cache: List[Dict[str, Any]] = []

def initialize_all_modems():
    global _initialized_modems_cache, active_modem_readers
    logger.info("Waiting 2 seconds before modem initialization...")
    time.sleep(2) 
    logger.info("Starting modem initialization process...")
    for port, reader_info in list(active_modem_readers.items()):
        logger.info(f"Stopping existing reader thread for {port}...")
        reader_info["stop_event"].set()
        reader_info["thread"].join(timeout=5)
        if reader_info["communicator"].ser.is_open: reader_info["communicator"].disconnect()
        del active_modem_readers[port]
    logger.info("All existing modem reader threads stopped.")
    _initialized_modems_cache = [] 
    raw_detected_modems = detect_modems()
    if not raw_detected_modems:
        logger.warning("No modems returned by detect_modems. Using example data for cache.")
        _initialized_modems_cache = get_example_modem_data(); return
    temp_initialized_list = []
    config_data = load_config()
    app_config = config_data if config_data is not None else {}
    default_baudrate = app_config.get("default_modem_baudrate", 115200)
    default_timeout = float(app_config.get("default_modem_timeout", 1.0))
    for modem_details_from_detection in raw_detected_modems:
        port = modem_details_from_detection.get("port")
        current_status = modem_details_from_detection.get("status")
        modem_model_str = str(modem_details_from_detection.get("model", "")).lower()
        modem_hint_str = str(modem_details_from_detection.get("modem_name_config", "")).lower() # modem_name_config has hint

        modem_info_for_cache = modem_details_from_detection.copy()
        modem_info_for_cache["sms_mode_status"] = "N/A"; modem_info_for_cache["cnmi_status"] = "N/A"

        is_novatel_modem = "novatel" in modem_model_str or "usb551l" in modem_model_str or \
                           "novatel" in modem_hint_str or "usb551l" in modem_hint_str

        if port and current_status == "RESPONSIVE":
            logger.info(f"Modem on port {port} ({modem_model_str if modem_model_str else 'Unknown Model'}) is responsive. Initializing for URC SMS reception.")
            communicator = ModemCommunicator(port=port, baudrate=default_baudrate, timeout=default_timeout)
            if communicator.connect():
                sms_mode_ok = set_sms_text_mode(communicator)
                modem_info_for_cache["sms_mode_status"] = "Text" if sms_mode_ok else "Error setting SMS mode"
                if sms_mode_ok:
                    # Determine CNMI parameters based on modem type
                    cnmi_mode_to_set = 2
                    cnmi_mt_to_set = 1 # Try mt=1 for all modems for direct +CMT URCs
                    cnmi_expected_prefix = f"{cnmi_mode_to_set},{cnmi_mt_to_set}"

                    cnmi_set_command_str = f"AT+CNMI={cnmi_mode_to_set},{cnmi_mt_to_set},0,0,0"
                    logger.info(f"Port {port} ({'Novatel' if is_novatel_modem else 'Other'}): Attempting to set CNMI with '{cnmi_set_command_str}' for +CMT URCs.")
                    cnmi_set_success = set_new_message_indications(communicator, mode=cnmi_mode_to_set, mt=cnmi_mt_to_set, bm=0, ds=0, bfr=0)

                    if cnmi_set_success:
                        logger.info(f"Port {port}: Command '{cnmi_set_command_str}' reported SUCCESS by modem.")
                        queried_cnmi_ok, queried_cnmi_values = get_cnmi_settings(communicator)
                        if queried_cnmi_ok:
                            modem_info_for_cache["cnmi_status"] = f"SetOK({cnmi_set_command_str}); Queried: '{queried_cnmi_values}'"
                            logger.info(f"Port {port}: Queried actual CNMI settings: '{queried_cnmi_values}'. Expected to start with '{cnmi_expected_prefix}' for +CMT URCs.")
                            if queried_cnmi_values.startswith(cnmi_expected_prefix):
                                logger.info(f"Port {port}: Queried CNMI '{queried_cnmi_values}' is compatible with +CMT URCs. Starting reader thread.")
                                stop_event = threading.Event()
                                thread = threading.Thread(target=modem_reader_thread_func, args=(port, communicator, stop_event), daemon=True)
                                thread.start()
                                active_modem_readers[port] = {"thread": thread, "communicator": communicator, "stop_event": stop_event}
                                logger.info(f"Successfully started reader thread for modem {port}.")
                            else:
                                logger.warning(f"Port {port}: Queried CNMI '{queried_cnmi_values}' DOES NOT start with '{cnmi_expected_prefix}'. Modem not configured for +CMT URCs. Reader thread will NOT start.")
                                modem_info_for_cache["cnmi_status"] += f" (Warning: Not {cnmi_expected_prefix},... URCs likely won't arrive as +CMT)"
                                communicator.disconnect()
                        else: # Failed to query CNMI
                            modem_info_for_cache["cnmi_status"] = f"SetOK({cnmi_set_command_str}); QueryCNMI_Failed"
                            logger.warning(f"Port {port}: Command '{cnmi_set_command_str}' succeeded, but FAILED to query back CNMI settings. Reader thread will NOT start as configuration is uncertain.")
                            communicator.disconnect()
                    else: # cnmi_set_success is False
                        modem_info_for_cache["cnmi_status"] = f"ErrorSettingCNMI({cnmi_set_command_str})"
                        logger.error(f"Port {port}: Failed to set CNMI using '{cnmi_set_command_str}'. Reader thread will NOT start. URCs disabled for this modem.")
                        communicator.disconnect()
                else: # sms_mode_ok is False
                    logger.error(f"Port {port}: Failed to set SMS text mode (AT+CMGF=1). Full SMS initialization aborted.");
                    if communicator and communicator.ser.is_open: communicator.disconnect()
            else: # communicator.connect() failed
                logger.error(f"Failed to connect to {port} for full initialization.");
                modem_info_for_cache["status"] = "ERROR: Connection Failed during init"
        else: # port not found or status not RESPONSIVE
            logger.warning(f"Modem on port {port if port else 'N/A'} not responsive (Status: {current_status}). Skipping full SMS initialization.")
        temp_initialized_list.append(modem_info_for_cache)
    _initialized_modems_cache = temp_initialized_list
    if not _initialized_modems_cache and raw_detected_modems: 
         logger.warning("Modem cache empty after processing, but raw_detected_modems existed. Fallback to example.")
         _initialized_modems_cache = get_example_modem_data()
    elif not _initialized_modems_cache: 
        logger.warning("Modem cache empty (no modems processed). Fallback to example.")
        _initialized_modems_cache = get_example_modem_data()
    logger.info(f"Modem initialization complete. Cached {len(_initialized_modems_cache)} modems. Active readers: {len(active_modem_readers)}")

def shutdown_all_modem_readers():
    """Attempts to stop all active modem reader threads and disconnect communicators."""
    logger.info("Attempting to shut down all active modem reader threads...")
    global active_modem_readers
    active_readers_copy = list(active_modem_readers.items()) # Iterate over a copy
    if not active_readers_copy:
        logger.info("No active modem readers found to shut down.")
        return

    for port, reader_info in active_readers_copy:
        thread = reader_info.get("thread")
        communicator = reader_info.get("communicator")
        stop_event = reader_info.get("stop_event")

        logger.info(f"Shutting down reader for port {port}...")
        if stop_event and hasattr(stop_event, 'set'):
            stop_event.set()
            logger.debug(f"Stop event set for {port}.")
        
        if thread and hasattr(thread, 'join') and thread.is_alive():
            logger.debug(f"Joining reader thread for {port} (timeout 5s)...")
            thread.join(timeout=5)
            if thread.is_alive():
                logger.warning(f"Reader thread for {port} did not terminate after 5s join timeout.")
            else:
                logger.info(f"Reader thread for {port} successfully joined.")
        elif thread and not thread.is_alive():
             logger.info(f"Reader thread for {port} was already stopped.")
        
        if communicator and hasattr(communicator, 'ser') and communicator.ser.is_open:
            logger.debug(f"Disconnecting communicator for {port}.")
            communicator.disconnect()
        
        if port in active_modem_readers: # Check if still there before deleting
            try:
                del active_modem_readers[port]
                logger.info(f"Removed {port} from active_modem_readers.")
            except KeyError:
                logger.warning(f"Port {port} was already removed from active_modem_readers during shutdown.")

    logger.info(f"Shutdown of {len(active_readers_copy)} modem reader(s) attempted. Remaining active readers: {len(active_modem_readers)}")

def get_modem_status() -> List[Dict[str, Any]]:
    global _initialized_modems_cache
    if not _initialized_modems_cache:
        logger.warning("Modem cache is empty at get_modem_status. Attempting on-demand initialization.")
        initialize_all_modems() 
        if not _initialized_modems_cache:
            logger.warning("Modem cache still empty after on-demand init. Returning example data directly.")
            return get_example_modem_data() 
    logger.info(f"Returning modem status. Cache size: {len(_initialized_modems_cache)}.")
    api_formatted_modems: List[Dict[str, Any]] = []
    if not _initialized_modems_cache: 
        logger.warning("Cache is unexpectedly empty before formatting loop in get_modem_status.")
        return [] 
    for modem_info in _initialized_modems_cache:
        if not isinstance(modem_info, dict):
            logger.warning(f"Skipping non-dict item in _initialized_modems_cache: {modem_info}"); continue
        modem_id_val = modem_info.get("id") 
        if not modem_id_val: modem_id_val = str(modem_info.get("port", "unknown_port")).replace("/", "_").replace("\\", "_")
        modem_name_val = modem_info.get("name") 
        if not modem_name_val: modem_name_val = modem_info.get("modem_name_config", modem_info.get("model", "Unknown Modem"))
        api_modem = {"id": modem_id_val, "name": modem_name_val,
                     "status": modem_info.get("status", "UNKNOWN"),
                     "signal_strength": f"{modem_info.get('signal_rssi', 'N/A')}" + (" dBm" if str(modem_info.get('signal_rssi', 'N/A')).isdigit() else ""),
                     "operator": modem_info.get("operator", "N/A"), "sim_status": modem_info.get("sim_status", "N/A"),
                     "phone_number": modem_info.get("phone_number", "N/A"), "imei": modem_info.get("imei", "N/A"),
                     "last_activity": modem_info.get("last_activity", "N/A"), "recent_sms": modem_info.get("recent_sms", []), 
                     "sms_mode_status": modem_info.get("sms_mode_status", "N/A"), "cnmi_status": modem_info.get("cnmi_status", "N/A"),
                     "port": modem_info.get("port")}
        api_formatted_modems.append(api_modem)
    if not api_formatted_modems and _initialized_modems_cache: logger.warning("api_formatted_modems list empty, but cache was not.")
    elif not api_formatted_modems and not _initialized_modems_cache: logger.info("api_formatted_modems empty because cache was empty.")
    return api_formatted_modems

def get_example_modem_data() -> List[Dict[str, Any]]:
    logger.info("Providing example modem data.")
    return [
        {"id": "modem1_example", "name": "Modem 1 (Example)", "status": "Connected (Example)", "signal_strength": "-70 dBm", "operator": "ExampleNet", "sim_status": "READY", "imei": "112233445566778", "last_activity": "N/A", "recent_sms": [], "sms_mode_status": "Text", "port": "EXAMPLE_PORT_1", "cnmi_status": "OK"},
        {"id": "modem2_example", "name": "Modem 2 (Example)", "status": "Offline (Example)", "signal_strength": "N/A", "operator": "N/A", "sim_status": "NO SIM", "imei": "223344556677889", "last_activity": "N/A", "recent_sms": [], "sms_mode_status": "N/A", "port": "EXAMPLE_PORT_2", "cnmi_status": "N/A"}
    ]

def fetch_and_store_new_sms_from_all_modems(): 
    global _initialized_modems_cache
    logger.info("Starting initial SMS sweep (fetch_and_store_new_sms_from_all_modems).")
    if not _initialized_modems_cache: logger.warning("Modem cache empty for initial SMS sweep."); return
    config_data = load_config()
    app_config = config_data if config_data is not None else {}
    default_baudrate = app_config.get("default_modem_baudrate", 115200)
    default_timeout = float(app_config.get("default_modem_timeout", 1.0))
    messages_added_count = 0; modems_processed_for_sweep = 0
    for modem_info in _initialized_modems_cache:
        port = modem_info.get("port") 
        status = modem_info.get("status")
        if not port or status != "RESPONSIVE" or modem_info.get("sms_mode_status") != "Text":
            logger.debug(f"Skipping modem {port} for initial SMS sweep: Status '{status}', SMS Mode '{modem_info.get('sms_mode_status')}'.")
            continue
        if port in active_modem_readers and active_modem_readers[port]["thread"].is_alive():
            logger.info(f"Reader thread for {port} is active. Skipping synchronous AT+CMGL sweep.")
            continue 
        modems_processed_for_sweep += 1
        logger.info(f"Performing initial SMS sweep on modem {port}.")
        temp_communicator = ModemCommunicator(port=port, baudrate=default_baudrate, timeout=default_timeout)
        try:
            if not temp_communicator.connect(): logger.error(f"Failed to connect to modem {port} for initial SMS sweep."); continue
            if not set_sms_text_mode(temp_communicator): logger.warning(f"Could not ensure text mode for {port} during sweep.")
            sms_list_result = list_sms(temp_communicator) 
            if not sms_list_result: logger.info(f"No SMS messages found on modem {port} during initial sweep."); continue
            logger.info(f"Found {len(sms_list_result)} messages on modem {port} during sweep. Attempting to store.")
            for sms_item in sms_list_result:
                timestamp_received_app = datetime.now().isoformat()
                message_data = {"modem_port": port, "modem_message_index": sms_item.get("index"), "sender": sms_item.get("sender"),
                                "timestamp_device": sms_item.get("timestamp"), "timestamp_received_app": timestamp_received_app, 
                                "message_body": sms_item.get("message"), "status_on_modem": sms_item.get("status")}
                # A more robust check for essential fields before adding to DB
                if not all(k in message_data and message_data[k] is not None for k in ["modem_port", "sender", "message_body", "status_on_modem"]):
                    logger.warning(f"Skipping SMS (sweep) due to missing essential data: Index {sms_item.get('index')} on {port}.")
                    continue
                db_id = add_message(message_data)
                if db_id: messages_added_count += 1
        except Exception as e: logger.error(f"Error during initial SMS sweep for modem {port}: {e}", exc_info=True)
        finally:
            if temp_communicator and temp_communicator.ser.is_open: temp_communicator.disconnect()
    logger.info(f"Finished initial SMS sweep. Processed {modems_processed_for_sweep} modems. Added {messages_added_count} messages.")

# The __main__ block below was for standalone testing and has been removed.
# It should not be part of the modem_manager module when used by the main application.