"""
Connection management module for the SMSHub application.

This module provides centralized connection management for database
and modem connections with pooling, retry logic, and graceful shutdown.
"""

import logging
import sqlite3
import threading
import time
import weakref
from contextlib import contextmanager
from typing import Dict, Any, Optional, List, Generator
from dataclasses import dataclass
from queue import Queue, Empty
from pathlib import Path

try:
    from .config_enhanced import get_enhanced_config
    from .error_handling import with_error_handling, ErrorCategory, ErrorSeverity, report_error
except ImportError:
    from config_enhanced import get_enhanced_config
    from error_handling import with_error_handling, ErrorCategory, ErrorSeverity, report_error

logger = logging.getLogger(__name__)


@dataclass
class ConnectionConfig:
    """Configuration for connection management."""
    max_connections: int = 10
    connection_timeout: float = 30.0
    retry_attempts: int = 3
    retry_delay: float = 1.0
    health_check_interval: float = 60.0
    idle_timeout: float = 300.0  # 5 minutes


class DatabaseConnectionPool:
    """
    Database connection pool with automatic connection management.
    
    Provides thread-safe database connections with pooling,
    health checks, and automatic cleanup.
    """

    def __init__(self, database_path: str, config: ConnectionConfig = None):
        """
        Initialize the database connection pool.
        
        Args:
            database_path: Path to the SQLite database
            config: Connection configuration
        """
        self.database_path = Path(database_path)
        self.config = config or ConnectionConfig()
        self._pool: Queue = Queue(maxsize=self.config.max_connections)
        self._active_connections: Dict[int, sqlite3.Connection] = {}
        self._connection_times: Dict[int, float] = {}
        self._lock = threading.RLock()
        self._shutdown = False
        self._health_check_thread: Optional[threading.Thread] = None
        
        # Initialize pool
        self._initialize_pool()
        self._start_health_check()

    def _initialize_pool(self) -> None:
        """Initialize the connection pool with connections."""
        try:
            # Ensure database directory exists
            self.database_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create initial connections
            for _ in range(min(3, self.config.max_connections)):
                conn = self._create_connection()
                if conn:
                    self._pool.put(conn)
                    
            logger.info(f"Database connection pool initialized with {self._pool.qsize()} connections")
            
        except Exception as e:
            logger.error(f"Error initializing database connection pool: {e}")
            report_error(
                f"Failed to initialize database connection pool: {str(e)}",
                "DatabaseConnectionPool",
                ErrorCategory.DATABASE,
                ErrorSeverity.CRITICAL,
                e
            )

    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """Create a new database connection."""
        try:
            conn = sqlite3.connect(
                str(self.database_path),
                timeout=self.config.connection_timeout,
                check_same_thread=False
            )
            
            # Configure connection
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            
            return conn
            
        except Exception as e:
            logger.error(f"Error creating database connection: {e}")
            return None

    @contextmanager
    def get_connection(self) -> Generator[sqlite3.Connection, None, None]:
        """
        Get a database connection from the pool.
        
        Yields:
            Database connection
        """
        if self._shutdown:
            raise RuntimeError("Connection pool is shut down")
            
        conn = None
        conn_id = None
        
        try:
            # Try to get connection from pool
            try:
                conn = self._pool.get(timeout=self.config.connection_timeout)
            except Empty:
                # Pool is empty, create new connection if under limit
                with self._lock:
                    if len(self._active_connections) < self.config.max_connections:
                        conn = self._create_connection()
                        if not conn:
                            raise RuntimeError("Failed to create database connection")
                    else:
                        raise RuntimeError("Connection pool exhausted")
            
            # Test connection health
            if not self._test_connection(conn):
                conn.close()
                conn = self._create_connection()
                if not conn:
                    raise RuntimeError("Failed to create healthy database connection")
            
            # Track active connection
            conn_id = id(conn)
            with self._lock:
                self._active_connections[conn_id] = conn
                self._connection_times[conn_id] = time.time()
            
            yield conn
            
        except Exception as e:
            logger.error(f"Error getting database connection: {e}")
            report_error(
                f"Database connection error: {str(e)}",
                "DatabaseConnectionPool",
                ErrorCategory.DATABASE,
                ErrorSeverity.HIGH,
                e
            )
            raise
            
        finally:
            # Return connection to pool or close if unhealthy
            if conn and conn_id:
                with self._lock:
                    self._active_connections.pop(conn_id, None)
                    self._connection_times.pop(conn_id, None)
                
                if self._test_connection(conn) and not self._shutdown:
                    try:
                        self._pool.put_nowait(conn)
                    except:
                        conn.close()
                else:
                    conn.close()

    def _test_connection(self, conn: sqlite3.Connection) -> bool:
        """Test if a connection is healthy."""
        try:
            conn.execute("SELECT 1").fetchone()
            return True
        except Exception:
            return False

    def _start_health_check(self) -> None:
        """Start the health check thread."""
        if self._health_check_thread is None:
            self._health_check_thread = threading.Thread(
                target=self._health_check_loop,
                daemon=True,
                name="DBHealthCheck"
            )
            self._health_check_thread.start()

    def _health_check_loop(self) -> None:
        """Health check loop for cleaning up idle connections."""
        while not self._shutdown:
            try:
                time.sleep(self.config.health_check_interval)
                self._cleanup_idle_connections()
            except Exception as e:
                logger.error(f"Error in database health check: {e}")

    def _cleanup_idle_connections(self) -> None:
        """Clean up idle connections."""
        current_time = time.time()
        idle_connections = []
        
        with self._lock:
            for conn_id, conn_time in self._connection_times.items():
                if current_time - conn_time > self.config.idle_timeout:
                    idle_connections.append(conn_id)
        
        for conn_id in idle_connections:
            with self._lock:
                conn = self._active_connections.pop(conn_id, None)
                self._connection_times.pop(conn_id, None)
                if conn:
                    conn.close()

    def get_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        with self._lock:
            return {
                "pool_size": self._pool.qsize(),
                "active_connections": len(self._active_connections),
                "max_connections": self.config.max_connections,
                "database_path": str(self.database_path),
                "shutdown": self._shutdown
            }

    def shutdown(self) -> None:
        """Shutdown the connection pool."""
        logger.info("Shutting down database connection pool...")
        self._shutdown = True
        
        # Close all active connections
        with self._lock:
            for conn in self._active_connections.values():
                try:
                    conn.close()
                except Exception as e:
                    logger.error(f"Error closing active connection: {e}")
            self._active_connections.clear()
            self._connection_times.clear()
        
        # Close pooled connections
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except Exception as e:
                logger.error(f"Error closing pooled connection: {e}")
        
        logger.info("Database connection pool shut down complete")


class ModemConnectionManager:
    """
    Manager for modem serial connections with health monitoring.
    
    Provides centralized management of modem connections with
    automatic reconnection and health monitoring.
    """

    def __init__(self, config: ConnectionConfig = None):
        """Initialize the modem connection manager."""
        self.config = config or ConnectionConfig()
        self._connections: Dict[str, Any] = {}  # port -> connection info
        self._lock = threading.RLock()
        self._shutdown = False
        self._health_check_thread: Optional[threading.Thread] = None
        
        self._start_health_monitoring()

    def register_connection(self, port: str, communicator) -> None:
        """
        Register a modem connection for monitoring.
        
        Args:
            port: Serial port identifier
            communicator: ModemCommunicator instance
        """
        with self._lock:
            self._connections[port] = {
                "communicator": communicator,
                "last_health_check": time.time(),
                "consecutive_failures": 0,
                "registered_at": time.time()
            }
            logger.info(f"Registered modem connection for monitoring: {port}")

    def unregister_connection(self, port: str) -> None:
        """
        Unregister a modem connection.
        
        Args:
            port: Serial port identifier
        """
        with self._lock:
            if port in self._connections:
                del self._connections[port]
                logger.info(f"Unregistered modem connection: {port}")

    def get_connection_health(self, port: str) -> Dict[str, Any]:
        """
        Get health information for a connection.
        
        Args:
            port: Serial port identifier
            
        Returns:
            Health information dictionary
        """
        with self._lock:
            conn_info = self._connections.get(port)
            if not conn_info:
                return {"status": "not_registered"}
            
            return {
                "status": "healthy" if conn_info["consecutive_failures"] < 3 else "unhealthy",
                "last_health_check": conn_info["last_health_check"],
                "consecutive_failures": conn_info["consecutive_failures"],
                "registered_at": conn_info["registered_at"],
                "uptime": time.time() - conn_info["registered_at"]
            }

    def _start_health_monitoring(self) -> None:
        """Start health monitoring thread."""
        if self._health_check_thread is None:
            self._health_check_thread = threading.Thread(
                target=self._health_monitoring_loop,
                daemon=True,
                name="ModemHealthCheck"
            )
            self._health_check_thread.start()

    def _health_monitoring_loop(self) -> None:
        """Health monitoring loop."""
        while not self._shutdown:
            try:
                time.sleep(self.config.health_check_interval)
                self._check_all_connections()
            except Exception as e:
                logger.error(f"Error in modem health monitoring: {e}")

    def _check_all_connections(self) -> None:
        """Check health of all registered connections."""
        current_time = time.time()
        
        with self._lock:
            for port, conn_info in self._connections.items():
                try:
                    communicator = conn_info["communicator"]
                    
                    # Test connection with simple AT command
                    if hasattr(communicator, 'send_at_command'):
                        success, _ = communicator.send_at_command("AT", timeout_override=5.0)
                        
                        if success:
                            conn_info["consecutive_failures"] = 0
                        else:
                            conn_info["consecutive_failures"] += 1
                            
                        conn_info["last_health_check"] = current_time
                        
                        if conn_info["consecutive_failures"] >= 3:
                            logger.warning(f"Modem connection {port} is unhealthy (3+ consecutive failures)")
                            
                except Exception as e:
                    logger.error(f"Error checking health of modem {port}: {e}")
                    conn_info["consecutive_failures"] += 1

    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all connections."""
        with self._lock:
            stats = {
                "total_connections": len(self._connections),
                "healthy_connections": 0,
                "unhealthy_connections": 0,
                "connections": {}
            }
            
            for port, conn_info in self._connections.items():
                health = self.get_connection_health(port)
                stats["connections"][port] = health
                
                if health["status"] == "healthy":
                    stats["healthy_connections"] += 1
                else:
                    stats["unhealthy_connections"] += 1
            
            return stats

    def shutdown(self) -> None:
        """Shutdown the connection manager."""
        logger.info("Shutting down modem connection manager...")
        self._shutdown = True
        
        with self._lock:
            self._connections.clear()
        
        logger.info("Modem connection manager shut down complete")


# Global instances
_db_pool: Optional[DatabaseConnectionPool] = None
_modem_manager: Optional[ModemConnectionManager] = None


def get_database_pool() -> DatabaseConnectionPool:
    """Get the global database connection pool."""
    global _db_pool
    if _db_pool is None:
        config = get_enhanced_config()
        db_path = Path(__file__).parent.parent / "sms_database.db"
        _db_pool = DatabaseConnectionPool(str(db_path))
    return _db_pool


def get_modem_connection_manager() -> ModemConnectionManager:
    """Get the global modem connection manager."""
    global _modem_manager
    if _modem_manager is None:
        _modem_manager = ModemConnectionManager()
    return _modem_manager


def shutdown_all_connections() -> None:
    """Shutdown all connection managers."""
    global _db_pool, _modem_manager
    
    if _db_pool:
        _db_pool.shutdown()
        _db_pool = None
    
    if _modem_manager:
        _modem_manager.shutdown()
        _modem_manager = None
    
    logger.info("All connection managers shut down")
