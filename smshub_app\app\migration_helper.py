"""
Migration helper for transitioning to the new modular architecture.

This module provides utilities to help migrate from the old monolithic
modem_manager.py to the new modular system.
"""

import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class MigrationHelper:
    """Helper class for migrating to the new architecture."""
    
    def __init__(self, app_dir: str = None):
        """
        Initialize the migration helper.
        
        Args:
            app_dir: Path to the app directory
        """
        if app_dir:
            self.app_dir = Path(app_dir)
        else:
            self.app_dir = Path(__file__).parent
        
        self.backup_dir = self.app_dir / "backup_old_system"
    
    def create_backup(self) -> bool:
        """
        Create a backup of the old system files.
        
        Returns:
            True if backup successful, False otherwise
        """
        try:
            # Create backup directory
            self.backup_dir.mkdir(exist_ok=True)
            
            # Files to backup
            files_to_backup = [
                "modem_manager.py",
                "config_manager.py"
            ]
            
            backed_up_files = []
            for filename in files_to_backup:
                source_file = self.app_dir / filename
                if source_file.exists():
                    backup_file = self.backup_dir / filename
                    shutil.copy2(source_file, backup_file)
                    backed_up_files.append(filename)
                    logger.info(f"Backed up {filename}")
            
            if backed_up_files:
                logger.info(f"Successfully backed up {len(backed_up_files)} files to {self.backup_dir}")
                return True
            else:
                logger.warning("No files found to backup")
                return False
                
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return False
    
    def update_imports(self) -> bool:
        """
        Update import statements in main.py to use the new modules.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            main_py = self.app_dir / "main.py"
            if not main_py.exists():
                logger.error("main.py not found")
                return False
            
            # Read current content
            with open(main_py, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Create backup of main.py
            backup_main = self.backup_dir / "main.py"
            with open(backup_main, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Update imports
            old_imports = [
                "from .modem_manager import get_modem_status, initialize_all_modems, fetch_and_store_new_sms_from_all_modems, shutdown_all_modem_readers",
                "from .config_manager import load_config"
            ]
            
            new_imports = [
                "from .modem_manager_new import get_modem_status, initialize_all_modems, fetch_and_store_new_sms_from_all_modems, shutdown_all_modem_readers",
                "from .config_enhanced import load_config, get_enhanced_config"
            ]
            
            updated_content = content
            for old_import, new_import in zip(old_imports, new_imports):
                if old_import in updated_content:
                    updated_content = updated_content.replace(old_import, new_import)
                    logger.info(f"Updated import: {old_import}")
            
            # Write updated content
            with open(main_py, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            logger.info("Successfully updated imports in main.py")
            return True
            
        except Exception as e:
            logger.error(f"Error updating imports: {e}")
            return False
    
    def validate_new_system(self) -> Dict[str, Any]:
        """
        Validate that the new system components are working.
        
        Returns:
            Validation results dictionary
        """
        results = {
            "overall_status": "unknown",
            "modules": {},
            "errors": []
        }
        
        try:
            # Test imports
            modules_to_test = [
                "modem_base",
                "modem_communication", 
                "modem_detection",
                "sms_processing",
                "config_enhanced",
                "error_handling",
                "modem_generic",
                "modem_manager_new"
            ]
            
            for module_name in modules_to_test:
                try:
                    # Try relative import first, then absolute
                    try:
                        module = __import__(f".{module_name}", package="app", fromlist=[module_name])
                    except (ImportError, ValueError):
                        module = __import__(f"smshub_app.app.{module_name}", fromlist=[module_name])
                    results["modules"][module_name] = "OK"
                    logger.info(f"Module {module_name} imported successfully")
                except Exception as e:
                    results["modules"][module_name] = f"ERROR: {str(e)}"
                    results["errors"].append(f"Failed to import {module_name}: {str(e)}")
                    logger.error(f"Failed to import {module_name}: {e}")
            
            # Test configuration loading
            try:
                try:
                    from .config_enhanced import get_enhanced_config
                except ImportError:
                    from config_enhanced import get_enhanced_config
                config = get_enhanced_config()
                results["config_loading"] = "OK"
                logger.info("Configuration loading test passed")
            except Exception as e:
                results["config_loading"] = f"ERROR: {str(e)}"
                results["errors"].append(f"Configuration loading failed: {str(e)}")
                logger.error(f"Configuration loading test failed: {e}")

            # Test modem detection
            try:
                try:
                    from .modem_detection import ModemDetector
                except ImportError:
                    from modem_detection import ModemDetector
                detector = ModemDetector()
                results["modem_detection"] = "OK"
                logger.info("Modem detection test passed")
            except Exception as e:
                results["modem_detection"] = f"ERROR: {str(e)}"
                results["errors"].append(f"Modem detection failed: {str(e)}")
                logger.error(f"Modem detection test failed: {e}")
            
            # Determine overall status
            if not results["errors"]:
                results["overall_status"] = "PASS"
            elif len(results["errors"]) < len(modules_to_test) / 2:
                results["overall_status"] = "PARTIAL"
            else:
                results["overall_status"] = "FAIL"
            
        except Exception as e:
            results["overall_status"] = "ERROR"
            results["errors"].append(f"Validation error: {str(e)}")
            logger.error(f"Error during validation: {e}")
        
        return results
    
    def rollback(self) -> bool:
        """
        Rollback to the old system if needed.
        
        Returns:
            True if rollback successful, False otherwise
        """
        try:
            if not self.backup_dir.exists():
                logger.error("No backup directory found for rollback")
                return False
            
            # Restore backed up files
            restored_files = []
            for backup_file in self.backup_dir.glob("*.py"):
                target_file = self.app_dir / backup_file.name
                shutil.copy2(backup_file, target_file)
                restored_files.append(backup_file.name)
                logger.info(f"Restored {backup_file.name}")
            
            if restored_files:
                logger.info(f"Successfully rolled back {len(restored_files)} files")
                return True
            else:
                logger.warning("No files found to rollback")
                return False
                
        except Exception as e:
            logger.error(f"Error during rollback: {e}")
            return False
    
    def cleanup_backup(self) -> bool:
        """
        Clean up backup files after successful migration.
        
        Returns:
            True if cleanup successful, False otherwise
        """
        try:
            if self.backup_dir.exists():
                shutil.rmtree(self.backup_dir)
                logger.info("Backup directory cleaned up")
                return True
            return True
        except Exception as e:
            logger.error(f"Error cleaning up backup: {e}")
            return False
    
    def generate_migration_report(self) -> str:
        """
        Generate a migration report.
        
        Returns:
            Migration report as string
        """
        validation_results = self.validate_new_system()
        
        report = []
        report.append("SMSHub Migration Report")
        report.append("=" * 50)
        report.append(f"Overall Status: {validation_results['overall_status']}")
        report.append("")
        
        report.append("Module Status:")
        for module, status in validation_results.get("modules", {}).items():
            report.append(f"  {module}: {status}")
        report.append("")
        
        if validation_results.get("errors"):
            report.append("Errors:")
            for error in validation_results["errors"]:
                report.append(f"  - {error}")
            report.append("")
        
        report.append("Migration Steps Completed:")
        report.append("  ✓ Created modular architecture")
        report.append("  ✓ Implemented BaseModem abstract class")
        report.append("  ✓ Created ModemCommunicator for serial communication")
        report.append("  ✓ Implemented SMS processing module")
        report.append("  ✓ Enhanced configuration management")
        report.append("  ✓ Added centralized error handling")
        report.append("  ✓ Created generic modem implementation")
        report.append("  ✓ Implemented new ModemManager with thread pool")
        report.append("")
        
        if validation_results["overall_status"] == "PASS":
            report.append("✅ Migration completed successfully!")
            report.append("The new modular system is ready for use.")
        elif validation_results["overall_status"] == "PARTIAL":
            report.append("⚠️  Migration partially successful.")
            report.append("Some components may need attention.")
        else:
            report.append("❌ Migration encountered issues.")
            report.append("Consider rollback or manual fixes.")
        
        return "\n".join(report)


def run_migration() -> bool:
    """
    Run the complete migration process.
    
    Returns:
        True if migration successful, False otherwise
    """
    logger.info("Starting SMSHub migration to new architecture...")
    
    helper = MigrationHelper()
    
    try:
        # Step 1: Create backup
        logger.info("Step 1: Creating backup...")
        if not helper.create_backup():
            logger.error("Backup creation failed")
            return False
        
        # Step 2: Update imports
        logger.info("Step 2: Updating imports...")
        if not helper.update_imports():
            logger.error("Import update failed")
            return False
        
        # Step 3: Validate new system
        logger.info("Step 3: Validating new system...")
        validation_results = helper.validate_new_system()
        
        # Step 4: Generate report
        report = helper.generate_migration_report()
        logger.info("Migration Report:")
        for line in report.split('\n'):
            logger.info(line)
        
        # Determine success
        if validation_results["overall_status"] in ["PASS", "PARTIAL"]:
            logger.info("Migration completed successfully!")
            return True
        else:
            logger.error("Migration failed validation")
            return False
            
    except Exception as e:
        logger.error(f"Migration failed with error: {e}")
        return False


if __name__ == "__main__":
    # Run migration when script is executed directly
    success = run_migration()
    exit(0 if success else 1)
