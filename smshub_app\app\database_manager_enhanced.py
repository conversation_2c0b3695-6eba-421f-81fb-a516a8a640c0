"""
Enhanced database manager for the SMSHub application.

This module provides database operations using connection pooling
and enhanced error handling for better reliability.
"""

import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from contextlib import contextmanager

try:
    from .connection_manager import get_database_pool
    from .logging_enhanced import get_logger, operation_timer
    from .error_handling import with_error_handling, ErrorCategory, ErrorSeverity
except ImportError:
    from connection_manager import get_database_pool
    from logging_enhanced import get_logger, operation_timer
    from error_handling import with_error_handling, ErrorCategory, ErrorSeverity

logger = get_logger(__name__, "DatabaseManager")


class DatabaseManager:
    """Enhanced database manager with connection pooling and monitoring."""
    
    def __init__(self):
        """Initialize the database manager."""
        self.pool = get_database_pool()
    
    @with_error_handling("DatabaseManager", ErrorCategory.DATABASE, ErrorSeverity.HIGH)
    def initialize_database(self) -> bool:
        """
        Initialize the database and create tables if they don't exist.
        
        Returns:
            True if initialization successful, False otherwise
        """
        with operation_timer(logger, "database_initialization"):
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # SMS Messages Table
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sms_messages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        modem_port TEXT NOT NULL,
                        modem_message_index INTEGER,
                        sender TEXT,
                        timestamp_device TEXT, 
                        timestamp_received_app TEXT NOT NULL,
                        message_body TEXT,
                        status_on_modem TEXT, 
                        is_read_in_app INTEGER DEFAULT 0, 
                        forwarding_status TEXT DEFAULT 'PENDING', 
                        forwarding_attempts INTEGER DEFAULT 0,
                        last_forwarding_error TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP, 
                        UNIQUE(modem_port, sender, timestamp_device, message_body)
                    )
                    """)
                    
                    # Create indexes for SMS messages
                    indexes = [
                        "CREATE INDEX IF NOT EXISTS idx_sms_modem_port ON sms_messages (modem_port)",
                        "CREATE INDEX IF NOT EXISTS idx_sms_timestamp_received_app ON sms_messages (timestamp_received_app)",
                        "CREATE INDEX IF NOT EXISTS idx_sms_forwarding_status ON sms_messages (forwarding_status)",
                        "CREATE INDEX IF NOT EXISTS idx_sms_sender ON sms_messages (sender)",
                        "CREATE INDEX IF NOT EXISTS idx_sms_created_at ON sms_messages (created_at)"
                    ]
                    
                    for index_sql in indexes:
                        cursor.execute(index_sql)
                    
                    # Activations Table
                    cursor.execute("""
                    CREATE TABLE IF NOT EXISTS activations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        modem_port TEXT NOT NULL,
                        phone_number TEXT NOT NULL,
                        service TEXT NOT NULL,
                        country TEXT,
                        operator TEXT,
                        smshub_reported_status INTEGER,
                        status_in_our_system TEXT NOT NULL DEFAULT 'PENDING_ISSUE',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        expires_at TEXT
                    )
                    """)
                    
                    # Create indexes for activations
                    activation_indexes = [
                        "CREATE INDEX IF NOT EXISTS idx_activations_status_ours ON activations (status_in_our_system)",
                        "CREATE INDEX IF NOT EXISTS idx_activations_expires_at ON activations (expires_at)",
                        "CREATE INDEX IF NOT EXISTS idx_activations_modem_service ON activations (modem_port, service, status_in_our_system)",
                        "CREATE INDEX IF NOT EXISTS idx_activations_created_at ON activations (created_at)"
                    ]
                    
                    for index_sql in activation_indexes:
                        cursor.execute(index_sql)
                    
                    conn.commit()
                    logger.info("Database initialized successfully")
                    return True
                    
            except Exception as e:
                logger.error(f"Error initializing database: {e}", exc_info=True)
                return False
    
    @with_error_handling("DatabaseManager", ErrorCategory.DATABASE, ErrorSeverity.MEDIUM)
    def add_message(self, message_data: Dict[str, Any]) -> Optional[int]:
        """
        Add an SMS message to the database.
        
        Args:
            message_data: Dictionary containing message information
            
        Returns:
            Message ID if successful, None otherwise
        """
        required_keys = ["modem_port", "sender", "message_body", "status_on_modem"]
        for key in required_keys:
            if key not in message_data:
                logger.error(f"Missing required key '{key}' in message_data")
                return None
        
        with operation_timer(logger, "add_message"):
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Set timestamp if not provided
                    if "timestamp_received_app" not in message_data:
                        message_data["timestamp_received_app"] = datetime.utcnow().isoformat()
                    
                    cursor.execute("""
                        INSERT OR IGNORE INTO sms_messages (
                            modem_port, modem_message_index, sender, timestamp_device, 
                            timestamp_received_app, message_body, status_on_modem, 
                            is_read_in_app, forwarding_status, forwarding_attempts, last_forwarding_error
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        message_data.get("modem_port"),
                        message_data.get("modem_message_index"),
                        message_data.get("sender"),
                        message_data.get("timestamp_device"),
                        message_data.get("timestamp_received_app"),
                        message_data.get("message_body"),
                        message_data.get("status_on_modem"),
                        message_data.get("is_read_in_app", 0),
                        message_data.get("forwarding_status", "PENDING"),
                        message_data.get("forwarding_attempts", 0),
                        message_data.get("last_forwarding_error")
                    ))
                    
                    conn.commit()
                    
                    if cursor.lastrowid and cursor.lastrowid > 0:
                        logger.info(f"Added message from {message_data.get('sender')} with ID {cursor.lastrowid}")
                        return cursor.lastrowid
                    elif cursor.rowcount == 0:
                        logger.debug(f"Message from {message_data.get('sender')} likely duplicate, not added")
                        return None
                    
                    return None
                    
            except Exception as e:
                logger.error(f"Error adding message to database: {e}", exc_info=True)
                return None
    
    @with_error_handling("DatabaseManager", ErrorCategory.DATABASE, ErrorSeverity.LOW)
    def get_all_messages(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Retrieve all SMS messages from the database.
        
        Args:
            limit: Maximum number of messages to retrieve
            offset: Number of messages to skip
            
        Returns:
            List of message dictionaries
        """
        with operation_timer(logger, "get_all_messages"):
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT id, modem_port, modem_message_index, sender, timestamp_device, 
                               timestamp_received_app, message_body, status_on_modem, is_read_in_app, 
                               forwarding_status, forwarding_attempts, last_forwarding_error, created_at
                        FROM sms_messages 
                        ORDER BY timestamp_received_app DESC, id DESC 
                        LIMIT ? OFFSET ?
                    """, (limit, offset))
                    
                    rows = cursor.fetchall()
                    messages = [dict(row) for row in rows]
                    
                    logger.debug(f"Retrieved {len(messages)} messages (limit {limit}, offset {offset})")
                    return messages
                    
            except Exception as e:
                logger.error(f"Error retrieving messages: {e}", exc_info=True)
                return []
    
    @with_error_handling("DatabaseManager", ErrorCategory.DATABASE, ErrorSeverity.MEDIUM)
    def get_pending_forward_messages(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get messages pending forwarding.
        
        Args:
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of pending message dictionaries
        """
        with operation_timer(logger, "get_pending_forward_messages"):
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT id, modem_port, modem_message_index, sender, timestamp_device, 
                               timestamp_received_app, message_body, status_on_modem, is_read_in_app, 
                               forwarding_status, forwarding_attempts, last_forwarding_error, created_at
                        FROM sms_messages 
                        WHERE forwarding_status IN ('PENDING', 'FAILED')
                        ORDER BY created_at ASC 
                        LIMIT ?
                    """, (limit,))
                    
                    rows = cursor.fetchall()
                    messages = [dict(row) for row in rows]
                    
                    logger.debug(f"Retrieved {len(messages)} pending forwarding messages")
                    return messages
                    
            except Exception as e:
                logger.error(f"Error retrieving pending messages: {e}", exc_info=True)
                return []
    
    @with_error_handling("DatabaseManager", ErrorCategory.DATABASE, ErrorSeverity.MEDIUM)
    def update_forwarding_status(self, message_id: int, status: str, 
                                attempts: Optional[int] = None, 
                                error_message: Optional[str] = None) -> bool:
        """
        Update the forwarding status of a message.
        
        Args:
            message_id: ID of the message to update
            status: New forwarding status
            attempts: Number of forwarding attempts
            error_message: Error message if failed
            
        Returns:
            True if update successful, False otherwise
        """
        with operation_timer(logger, "update_forwarding_status"):
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    update_fields = ["forwarding_status = ?"]
                    params = [status]
                    
                    if attempts is not None:
                        update_fields.append("forwarding_attempts = ?")
                        params.append(attempts)
                    
                    if error_message is not None:
                        update_fields.append("last_forwarding_error = ?")
                        params.append(error_message)
                    elif status in ["SUCCESS", "SKIPPED"]:
                        update_fields.append("last_forwarding_error = NULL")
                    
                    params.append(message_id)
                    
                    sql = f"UPDATE sms_messages SET {', '.join(update_fields)} WHERE id = ?"
                    cursor.execute(sql, tuple(params))
                    conn.commit()
                    
                    if cursor.rowcount > 0:
                        logger.info(f"Updated forwarding status for message {message_id} to {status}")
                        return True
                    else:
                        logger.warning(f"No message found with ID {message_id}")
                        return False
                        
            except Exception as e:
                logger.error(f"Error updating forwarding status: {e}", exc_info=True)
                return False
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.
        
        Returns:
            Dictionary with database statistics
        """
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # Message counts
                cursor.execute("SELECT COUNT(*) FROM sms_messages")
                stats["total_messages"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM sms_messages WHERE forwarding_status = 'PENDING'")
                stats["pending_messages"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM sms_messages WHERE forwarding_status = 'SUCCESS'")
                stats["forwarded_messages"] = cursor.fetchone()[0]
                
                # Activation counts
                cursor.execute("SELECT COUNT(*) FROM activations")
                stats["total_activations"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM activations WHERE status_in_our_system = 'PENDING_SMS'")
                stats["pending_activations"] = cursor.fetchone()[0]
                
                # Recent activity
                cursor.execute("""
                    SELECT COUNT(*) FROM sms_messages 
                    WHERE timestamp_received_app > datetime('now', '-1 hour')
                """)
                stats["messages_last_hour"] = cursor.fetchone()[0]
                
                # Connection pool stats
                stats["connection_pool"] = self.pool.get_stats()
                
                return stats
                
        except Exception as e:
            logger.error(f"Error getting database stats: {e}", exc_info=True)
            return {"error": str(e)}


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager


# Compatibility functions for existing code
def init_db() -> bool:
    """Initialize the database (compatibility function)."""
    manager = get_database_manager()
    return manager.initialize_database()


def add_message(message_data: Dict[str, Any]) -> Optional[int]:
    """Add a message to the database (compatibility function)."""
    manager = get_database_manager()
    return manager.add_message(message_data)


def get_all_messages(limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all messages (compatibility function)."""
    manager = get_database_manager()
    return manager.get_all_messages(limit, offset)


def get_pending_forward_messages(limit: int = 10) -> List[Dict[str, Any]]:
    """Get pending forward messages (compatibility function)."""
    manager = get_database_manager()
    return manager.get_pending_forward_messages(limit)


def update_forwarding_status(message_id: int, status: str, 
                           attempts: Optional[int] = None, 
                           error_message: Optional[str] = None) -> bool:
    """Update forwarding status (compatibility function)."""
    manager = get_database_manager()
    return manager.update_forwarding_status(message_id, status, attempts, error_message)
