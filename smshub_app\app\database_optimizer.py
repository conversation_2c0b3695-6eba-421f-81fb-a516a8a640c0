"""
Database optimization module for the SMSHub application.

This module provides database performance optimization including
query optimization, indexing, batch operations, and maintenance automation.
"""

import logging
import sqlite3
import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

try:
    from .connection_manager import get_database_pool
    from .logging_enhanced import get_logger, operation_timer
    from .error_handling import with_error_handling, ErrorCategory, ErrorSeverity
    from .caching_manager import get_caching_manager
except ImportError:
    from connection_manager import get_database_pool
    from logging_enhanced import get_logger, operation_timer
    from error_handling import with_error_handling, ErrorCategory, ErrorSeverity
    from caching_manager import get_caching_manager

logger = get_logger(__name__, "DatabaseOptimizer")


@dataclass
class QueryStats:
    """Statistics for database queries."""
    query_type: str
    execution_time: float
    rows_affected: int
    cache_hit: bool = False


class QueryOptimizer:
    """
    Query optimization and performance monitoring.
    
    Provides optimized queries, caching, and performance tracking
    for database operations.
    """

    def __init__(self):
        """Initialize query optimizer."""
        self.pool = get_database_pool()
        self.cache = get_caching_manager()
        self.query_stats: List[QueryStats] = []
        self.max_stats_history = 1000

    @with_error_handling("QueryOptimizer", ErrorCategory.DATABASE, ErrorSeverity.MEDIUM)
    def get_messages_optimized(self, limit: int = 100, offset: int = 0, 
                              use_cache: bool = True) -> List[Dict[str, Any]]:
        """
        Get messages with optimized query and caching.
        
        Args:
            limit: Maximum number of messages
            offset: Number of messages to skip
            use_cache: Whether to use caching
            
        Returns:
            List of message dictionaries
        """
        cache_key = f"messages:limit_{limit}:offset_{offset}"
        
        # Try cache first
        if use_cache:
            cached_result = self.cache.get(cache_key)
            if cached_result is not None:
                self._record_stats("get_messages", 0, len(cached_result), cache_hit=True)
                return cached_result

        with operation_timer(logger, "get_messages_optimized"):
            start_time = time.time()
            
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Optimized query with proper indexing
                    cursor.execute("""
                        SELECT id, modem_port, modem_message_index, sender, timestamp_device, 
                               timestamp_received_app, message_body, status_on_modem, is_read_in_app, 
                               forwarding_status, forwarding_attempts, last_forwarding_error, created_at
                        FROM sms_messages 
                        ORDER BY timestamp_received_app DESC, id DESC 
                        LIMIT ? OFFSET ?
                    """, (limit, offset))
                    
                    rows = cursor.fetchall()
                    messages = [dict(row) for row in rows]
                    
                    execution_time = time.time() - start_time
                    self._record_stats("get_messages", execution_time, len(messages))
                    
                    # Cache the result
                    if use_cache and messages:
                        self.cache.set(cache_key, messages, ttl=60)  # Cache for 1 minute
                    
                    return messages
                    
            except Exception as e:
                logger.error(f"Error in optimized message query: {e}")
                return []

    @with_error_handling("QueryOptimizer", ErrorCategory.DATABASE, ErrorSeverity.MEDIUM)
    def get_pending_messages_optimized(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get pending messages with optimized query.
        
        Args:
            limit: Maximum number of messages
            
        Returns:
            List of pending message dictionaries
        """
        cache_key = f"pending_messages:limit_{limit}"
        
        # Try cache first (short TTL for pending messages)
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            self._record_stats("get_pending_messages", 0, len(cached_result), cache_hit=True)
            return cached_result

        with operation_timer(logger, "get_pending_messages_optimized"):
            start_time = time.time()
            
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Optimized query using index on forwarding_status
                    cursor.execute("""
                        SELECT id, modem_port, modem_message_index, sender, timestamp_device, 
                               timestamp_received_app, message_body, status_on_modem, is_read_in_app, 
                               forwarding_status, forwarding_attempts, last_forwarding_error, created_at
                        FROM sms_messages 
                        WHERE forwarding_status IN ('PENDING', 'FAILED')
                        ORDER BY created_at ASC 
                        LIMIT ?
                    """, (limit,))
                    
                    rows = cursor.fetchall()
                    messages = [dict(row) for row in rows]
                    
                    execution_time = time.time() - start_time
                    self._record_stats("get_pending_messages", execution_time, len(messages))
                    
                    # Cache with short TTL
                    if messages:
                        self.cache.set(cache_key, messages, ttl=10)  # Cache for 10 seconds
                    
                    return messages
                    
            except Exception as e:
                logger.error(f"Error in optimized pending messages query: {e}")
                return []

    @with_error_handling("QueryOptimizer", ErrorCategory.DATABASE, ErrorSeverity.HIGH)
    def batch_update_forwarding_status(self, updates: List[Tuple[int, str, Optional[int], Optional[str]]]) -> bool:
        """
        Batch update forwarding status for multiple messages.
        
        Args:
            updates: List of tuples (message_id, status, attempts, error_message)
            
        Returns:
            True if successful, False otherwise
        """
        if not updates:
            return True

        with operation_timer(logger, f"batch_update_forwarding_status_{len(updates)}"):
            start_time = time.time()
            
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Prepare batch update
                    update_data = []
                    for message_id, status, attempts, error_message in updates:
                        update_data.append((status, attempts, error_message, message_id))
                    
                    # Execute batch update
                    cursor.executemany("""
                        UPDATE sms_messages 
                        SET forwarding_status = ?, forwarding_attempts = ?, last_forwarding_error = ?
                        WHERE id = ?
                    """, update_data)
                    
                    conn.commit()
                    
                    execution_time = time.time() - start_time
                    self._record_stats("batch_update_forwarding", execution_time, len(updates))
                    
                    # Invalidate related caches
                    self.cache.delete("pending_messages:limit_10")
                    
                    logger.info(f"Batch updated {len(updates)} message forwarding statuses")
                    return True
                    
            except Exception as e:
                logger.error(f"Error in batch update: {e}")
                return False

    @with_error_handling("QueryOptimizer", ErrorCategory.DATABASE, ErrorSeverity.MEDIUM)
    def batch_insert_messages(self, messages: List[Dict[str, Any]]) -> List[Optional[int]]:
        """
        Batch insert multiple messages.
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            List of message IDs (None for failed inserts)
        """
        if not messages:
            return []

        with operation_timer(logger, f"batch_insert_messages_{len(messages)}"):
            start_time = time.time()
            result_ids = []
            
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Prepare batch insert data
                    insert_data = []
                    for msg in messages:
                        insert_data.append((
                            msg.get("modem_port"),
                            msg.get("modem_message_index"),
                            msg.get("sender"),
                            msg.get("timestamp_device"),
                            msg.get("timestamp_received_app", datetime.utcnow().isoformat()),
                            msg.get("message_body"),
                            msg.get("status_on_modem"),
                            msg.get("is_read_in_app", 0),
                            msg.get("forwarding_status", "PENDING"),
                            msg.get("forwarding_attempts", 0),
                            msg.get("last_forwarding_error")
                        ))
                    
                    # Execute batch insert
                    cursor.executemany("""
                        INSERT OR IGNORE INTO sms_messages (
                            modem_port, modem_message_index, sender, timestamp_device, 
                            timestamp_received_app, message_body, status_on_modem, 
                            is_read_in_app, forwarding_status, forwarding_attempts, last_forwarding_error
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, insert_data)
                    
                    conn.commit()
                    
                    # Get the IDs of inserted messages (simplified approach)
                    for _ in messages:
                        result_ids.append(cursor.lastrowid if cursor.rowcount > 0 else None)
                    
                    execution_time = time.time() - start_time
                    self._record_stats("batch_insert_messages", execution_time, len(messages))
                    
                    # Invalidate message caches
                    self.cache.invalidate_sms_cache()
                    
                    logger.info(f"Batch inserted {len(messages)} messages")
                    return result_ids
                    
            except Exception as e:
                logger.error(f"Error in batch insert: {e}")
                return [None] * len(messages)

    def _record_stats(self, query_type: str, execution_time: float, 
                     rows_affected: int, cache_hit: bool = False) -> None:
        """Record query statistics."""
        stats = QueryStats(
            query_type=query_type,
            execution_time=execution_time,
            rows_affected=rows_affected,
            cache_hit=cache_hit
        )
        
        self.query_stats.append(stats)
        
        # Keep only recent stats
        if len(self.query_stats) > self.max_stats_history:
            self.query_stats = self.query_stats[-self.max_stats_history:]

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get query performance statistics."""
        if not self.query_stats:
            return {"total_queries": 0}

        stats_by_type = {}
        total_time = 0
        total_queries = len(self.query_stats)
        cache_hits = 0

        for stat in self.query_stats:
            if stat.query_type not in stats_by_type:
                stats_by_type[stat.query_type] = {
                    "count": 0,
                    "total_time": 0,
                    "avg_time": 0,
                    "total_rows": 0
                }
            
            stats_by_type[stat.query_type]["count"] += 1
            stats_by_type[stat.query_type]["total_time"] += stat.execution_time
            stats_by_type[stat.query_type]["total_rows"] += stat.rows_affected
            
            total_time += stat.execution_time
            if stat.cache_hit:
                cache_hits += 1

        # Calculate averages
        for query_type in stats_by_type:
            count = stats_by_type[query_type]["count"]
            stats_by_type[query_type]["avg_time"] = stats_by_type[query_type]["total_time"] / count

        return {
            "total_queries": total_queries,
            "total_execution_time": total_time,
            "avg_execution_time": total_time / total_queries if total_queries > 0 else 0,
            "cache_hit_rate": cache_hits / total_queries if total_queries > 0 else 0,
            "queries_by_type": stats_by_type
        }


class DatabaseMaintenance:
    """
    Database maintenance and optimization tasks.
    
    Provides automated maintenance tasks like vacuuming,
    index optimization, and cleanup operations.
    """

    def __init__(self):
        """Initialize database maintenance."""
        self.pool = get_database_pool()

    @with_error_handling("DatabaseMaintenance", ErrorCategory.DATABASE, ErrorSeverity.MEDIUM)
    def vacuum_database(self) -> bool:
        """
        Vacuum the database to reclaim space and optimize performance.
        
        Returns:
            True if successful, False otherwise
        """
        with operation_timer(logger, "vacuum_database"):
            try:
                with self.pool.get_connection() as conn:
                    # Enable WAL mode for better performance
                    conn.execute("PRAGMA journal_mode=WAL")
                    
                    # Vacuum the database
                    conn.execute("VACUUM")
                    
                    # Analyze tables for query optimization
                    conn.execute("ANALYZE")
                    
                    logger.info("Database vacuum and analyze completed successfully")
                    return True
                    
            except Exception as e:
                logger.error(f"Error during database vacuum: {e}")
                return False

    @with_error_handling("DatabaseMaintenance", ErrorCategory.DATABASE, ErrorSeverity.LOW)
    def cleanup_old_messages(self, days_old: int = 30) -> int:
        """
        Clean up old messages to maintain database performance.
        
        Args:
            days_old: Number of days after which messages are considered old
            
        Returns:
            Number of messages deleted
        """
        with operation_timer(logger, "cleanup_old_messages"):
            try:
                cutoff_date = (datetime.utcnow() - timedelta(days=days_old)).isoformat()
                
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Delete old messages that have been successfully forwarded
                    cursor.execute("""
                        DELETE FROM sms_messages 
                        WHERE timestamp_received_app < ? 
                        AND forwarding_status = 'SUCCESS'
                    """, (cutoff_date,))
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
                    
                    logger.info(f"Cleaned up {deleted_count} old messages (older than {days_old} days)")
                    return deleted_count
                    
            except Exception as e:
                logger.error(f"Error during message cleanup: {e}")
                return 0

    @with_error_handling("DatabaseMaintenance", ErrorCategory.DATABASE, ErrorSeverity.LOW)
    def optimize_indexes(self) -> bool:
        """
        Optimize database indexes for better performance.
        
        Returns:
            True if successful, False otherwise
        """
        with operation_timer(logger, "optimize_indexes"):
            try:
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # Reindex all indexes
                    cursor.execute("REINDEX")
                    
                    # Update table statistics
                    cursor.execute("ANALYZE")
                    
                    logger.info("Database indexes optimized successfully")
                    return True
                    
            except Exception as e:
                logger.error(f"Error optimizing indexes: {e}")
                return False

    def get_database_size_info(self) -> Dict[str, Any]:
        """Get database size and usage information."""
        try:
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # Get page count and page size
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                
                # Calculate database size
                db_size = page_count * page_size
                
                # Get table sizes
                cursor.execute("""
                    SELECT name, COUNT(*) as row_count 
                    FROM sqlite_master 
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """)
                tables = cursor.fetchall()
                
                table_info = {}
                for table_name, _ in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    row_count = cursor.fetchone()[0]
                    table_info[table_name] = {"rows": row_count}
                
                return {
                    "total_size_bytes": db_size,
                    "total_size_mb": round(db_size / (1024 * 1024), 2),
                    "page_count": page_count,
                    "page_size": page_size,
                    "tables": table_info
                }
                
        except Exception as e:
            logger.error(f"Error getting database size info: {e}")
            return {"error": str(e)}


# Global instances
_query_optimizer: Optional[QueryOptimizer] = None
_db_maintenance: Optional[DatabaseMaintenance] = None


def get_query_optimizer() -> QueryOptimizer:
    """Get the global query optimizer instance."""
    global _query_optimizer
    if _query_optimizer is None:
        _query_optimizer = QueryOptimizer()
    return _query_optimizer


def get_database_maintenance() -> DatabaseMaintenance:
    """Get the global database maintenance instance."""
    global _db_maintenance
    if _db_maintenance is None:
        _db_maintenance = DatabaseMaintenance()
    return _db_maintenance
