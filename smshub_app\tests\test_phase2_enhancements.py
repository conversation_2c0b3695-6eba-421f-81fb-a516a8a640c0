"""
Tests for Phase 2 reliability enhancements.

This module contains tests for the new reliability features including
connection management, enhanced logging, and threading improvements.
"""

import pytest
import sys
import os
import tempfile
import threading
import time
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add project root to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import Phase 2 modules
from smshub_app.app.connection_manager import DatabaseConnectionPool, ModemConnectionManager, ConnectionConfig
from smshub_app.app.logging_enhanced import LoggingManager, RequestContext, request_context, operation_timer
from smshub_app.app.threading_enhanced import DeadlockDetector, MonitoredLock
from smshub_app.app.database_manager_enhanced import DatabaseManager


class TestConnectionManager:
    """Test the connection management system."""
    
    def test_connection_config_creation(self):
        """Test ConnectionConfig creation."""
        config = ConnectionConfig()
        assert config.max_connections == 10
        assert config.connection_timeout == 30.0
        assert config.retry_attempts == 3
    
    def test_database_connection_pool_creation(self):
        """Test DatabaseConnectionPool creation."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            pool = DatabaseConnectionPool(tmp.name)
            assert pool.database_path == Path(tmp.name)
            assert pool.config.max_connections == 10
            
            # Cleanup
            pool.shutdown()
            os.unlink(tmp.name)
    
    def test_database_connection_pool_get_connection(self):
        """Test getting connections from the pool."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            pool = DatabaseConnectionPool(tmp.name)
            
            try:
                with pool.get_connection() as conn:
                    assert conn is not None
                    # Test that we can execute a query
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    assert result[0] == 1
            finally:
                pool.shutdown()
                os.unlink(tmp.name)
    
    def test_database_connection_pool_stats(self):
        """Test connection pool statistics."""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
            pool = DatabaseConnectionPool(tmp.name)
            
            try:
                stats = pool.get_stats()
                assert "pool_size" in stats
                assert "active_connections" in stats
                assert "max_connections" in stats
                assert stats["max_connections"] == 10
            finally:
                pool.shutdown()
                os.unlink(tmp.name)
    
    def test_modem_connection_manager(self):
        """Test ModemConnectionManager functionality."""
        manager = ModemConnectionManager()
        
        # Mock communicator
        mock_comm = Mock()
        mock_comm.send_at_command.return_value = (True, ["OK"])
        
        # Register connection
        manager.register_connection("COM1", mock_comm)
        
        # Get health
        health = manager.get_connection_health("COM1")
        assert health["status"] in ["healthy", "unhealthy"]
        
        # Get stats
        stats = manager.get_all_stats()
        assert stats["total_connections"] == 1
        assert "COM1" in stats["connections"]
        
        # Cleanup
        manager.shutdown()


class TestLoggingEnhanced:
    """Test the enhanced logging system."""
    
    def test_logging_manager_creation(self):
        """Test LoggingManager creation."""
        with patch('smshub_app.app.logging_enhanced.get_enhanced_config') as mock_config:
            mock_config.return_value = Mock()
            mock_config.return_value.logging = Mock()
            mock_config.return_value.logging.level = "INFO"
            
            manager = LoggingManager()
            assert manager is not None
    
    def test_request_context(self):
        """Test request context functionality."""
        context = RequestContext()
        
        # Test setting and getting request ID
        context.request_id = "test-123"
        assert context.request_id == "test-123"
        
        # Test setting and getting component
        context.component = "TestComponent"
        assert context.component == "TestComponent"
        
        # Test clearing context
        context.clear()
        assert context.request_id is None
        assert context.component is None
    
    def test_request_context_manager(self):
        """Test request context manager."""
        with request_context(request_id="test-456", component="TestComp") as req_id:
            assert req_id == "test-456"
            # Context should be set within the block
    
    @patch('smshub_app.app.logging_enhanced.get_enhanced_config')
    def test_logging_configuration(self, mock_config):
        """Test logging configuration."""
        mock_config.return_value = Mock()
        mock_config.return_value.logging = Mock()
        mock_config.return_value.logging.level = "DEBUG"
        
        manager = LoggingManager()
        manager.configure_logging("testing")
        
        assert manager._configured is True
    
    def test_operation_timer(self):
        """Test operation timer context manager."""
        mock_logger = Mock()
        
        with operation_timer(mock_logger, "test_operation"):
            time.sleep(0.01)  # Small delay
        
        # Verify logger was called
        assert mock_logger.info.call_count >= 2  # Start and end messages


class TestThreadingEnhanced:
    """Test the enhanced threading system."""
    
    def test_deadlock_detector_creation(self):
        """Test DeadlockDetector creation."""
        detector = DeadlockDetector(check_interval=1.0)
        assert detector.check_interval == 1.0
        assert not detector._running
    
    def test_deadlock_detector_start_stop(self):
        """Test starting and stopping the deadlock detector."""
        detector = DeadlockDetector(check_interval=0.1)
        
        # Start detector
        detector.start()
        assert detector._running is True
        
        # Stop detector
        detector.stop()
        assert detector._running is False
    
    def test_monitored_lock_creation(self):
        """Test MonitoredLock creation."""
        detector = DeadlockDetector()
        lock = MonitoredLock(name="test_lock", detector=detector)
        assert lock._name == "test_lock"
        assert lock._detector == detector
    
    def test_monitored_lock_acquire_release(self):
        """Test MonitoredLock acquire and release."""
        detector = DeadlockDetector()
        lock = MonitoredLock(name="test_lock", detector=detector)
        
        # Test acquire and release
        result = lock.acquire(blocking=False)
        assert result is True
        
        lock.release()
        # Should not raise an exception
    
    def test_monitored_lock_context_manager(self):
        """Test MonitoredLock as context manager."""
        detector = DeadlockDetector()
        lock = MonitoredLock(name="test_lock", detector=detector)
        
        with lock:
            # Lock should be acquired
            pass
        # Lock should be released automatically
    
    def test_deadlock_detector_stats(self):
        """Test deadlock detector statistics."""
        detector = DeadlockDetector()
        lock = MonitoredLock(name="test_lock", detector=detector)
        
        stats = detector.get_stats()
        assert "running" in stats
        assert "monitored_locks" in stats
        assert "monitored_threads" in stats


class TestDatabaseManagerEnhanced:
    """Test the enhanced database manager."""
    
    @patch('smshub_app.app.database_manager_enhanced.get_database_pool')
    def test_database_manager_creation(self, mock_pool):
        """Test DatabaseManager creation."""
        mock_pool.return_value = Mock()
        
        manager = DatabaseManager()
        assert manager.pool is not None
    
    @patch('smshub_app.app.database_manager_enhanced.get_database_pool')
    def test_database_initialization(self, mock_pool):
        """Test database initialization."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_conn.cursor.return_value = mock_cursor
        mock_pool.return_value.get_connection.return_value.__enter__.return_value = mock_conn
        
        manager = DatabaseManager()
        result = manager.initialize_database()
        
        assert result is True
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
    
    @patch('smshub_app.app.database_manager_enhanced.get_database_pool')
    def test_add_message(self, mock_pool):
        """Test adding a message."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.lastrowid = 123
        mock_conn.cursor.return_value = mock_cursor
        mock_pool.return_value.get_connection.return_value.__enter__.return_value = mock_conn
        
        manager = DatabaseManager()
        message_data = {
            "modem_port": "COM1",
            "sender": "+1234567890",
            "message_body": "Test message",
            "status_on_modem": "UNREAD"
        }
        
        result = manager.add_message(message_data)
        
        assert result == 123
        mock_cursor.execute.assert_called()
        mock_conn.commit.assert_called()
    
    @patch('smshub_app.app.database_manager_enhanced.get_database_pool')
    def test_get_all_messages(self, mock_pool):
        """Test getting all messages."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.fetchall.return_value = [
            {"id": 1, "sender": "+1234567890", "message_body": "Test"}
        ]
        mock_conn.cursor.return_value = mock_cursor
        mock_pool.return_value.get_connection.return_value.__enter__.return_value = mock_conn
        
        manager = DatabaseManager()
        messages = manager.get_all_messages(limit=10)
        
        assert len(messages) == 1
        mock_cursor.execute.assert_called()
    
    @patch('smshub_app.app.database_manager_enhanced.get_database_pool')
    def test_database_stats(self, mock_pool):
        """Test getting database statistics."""
        mock_conn = Mock()
        mock_cursor = Mock()
        mock_cursor.fetchone.return_value = [100]  # Mock count result
        mock_conn.cursor.return_value = mock_cursor
        mock_pool.return_value.get_connection.return_value.__enter__.return_value = mock_conn
        mock_pool.return_value.get_stats.return_value = {"pool_size": 5}
        
        manager = DatabaseManager()
        stats = manager.get_database_stats()
        
        assert "total_messages" in stats
        assert "connection_pool" in stats


class TestIntegrationPhase2:
    """Integration tests for Phase 2 enhancements."""
    
    def test_logging_with_connection_manager(self):
        """Test logging integration with connection manager."""
        with patch('smshub_app.app.logging_enhanced.get_enhanced_config') as mock_config:
            mock_config.return_value = Mock()
            mock_config.return_value.logging = Mock()
            mock_config.return_value.logging.level = "INFO"
            
            # This should not raise an exception
            manager = LoggingManager()
            manager.configure_logging("testing")
    
    def test_error_handling_with_database(self):
        """Test error handling integration with database operations."""
        with patch('smshub_app.app.database_manager_enhanced.get_database_pool') as mock_pool:
            # Mock a database error
            mock_pool.return_value.get_connection.side_effect = Exception("Database error")
            
            manager = DatabaseManager()
            result = manager.add_message({
                "modem_port": "COM1",
                "sender": "+1234567890", 
                "message_body": "Test",
                "status_on_modem": "UNREAD"
            })
            
            # Should handle error gracefully
            assert result is None
    
    def test_threading_with_logging(self):
        """Test threading enhancements with logging."""
        detector = DeadlockDetector(check_interval=0.1)
        
        try:
            detector.start()
            
            # Create some locks
            lock1 = MonitoredLock(name="lock1", detector=detector)
            lock2 = MonitoredLock(name="lock2", detector=detector)
            
            # Use locks briefly
            with lock1:
                with lock2:
                    pass
            
            # Get stats
            stats = detector.get_stats()
            assert stats["monitored_locks"] >= 2
            
        finally:
            detector.stop()


def test_phase2_compatibility():
    """Test that Phase 2 maintains compatibility with existing APIs."""
    # Test that we can import compatibility functions
    try:
        from smshub_app.app.database_manager_enhanced import (
            init_db, add_message, get_all_messages, 
            get_pending_forward_messages, update_forwarding_status
        )
        
        # These should be callable
        assert callable(init_db)
        assert callable(add_message)
        assert callable(get_all_messages)
        assert callable(get_pending_forward_messages)
        assert callable(update_forwarding_status)
        
    except ImportError as e:
        pytest.fail(f"Phase 2 compatibility test failed: {e}")


if __name__ == "__main__":
    # Run tests when script is executed directly
    pytest.main([__file__, "-v"])
