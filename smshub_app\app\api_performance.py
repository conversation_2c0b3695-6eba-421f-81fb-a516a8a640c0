"""
API performance optimization module for the SMSHub application.

This module provides API performance enhancements including response compression,
caching, pagination, and async request handling.
"""

import gzip
import json
import time
from functools import wraps
from typing import Any, Dict, List, Optional, Tuple, Callable
from flask import request, Response, jsonify, g
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

try:
    from .caching_manager import get_caching_manager
    from .logging_enhanced import get_logger, operation_timer, request_context
    from .error_handling import with_error_handling, ErrorCategory, ErrorSeverity
    from .database_optimizer import get_query_optimizer
except ImportError:
    from caching_manager import get_caching_manager
    from logging_enhanced import get_logger, operation_timer, request_context
    from error_handling import with_error_handling, ErrorCategory, ErrorSeverity
    from database_optimizer import get_query_optimizer

logger = get_logger(__name__, "APIPerformance")


class ResponseCompressor:
    """
    Response compression utility for API endpoints.
    
    Provides gzip compression for API responses to reduce
    bandwidth usage and improve response times.
    """

    @staticmethod
    def should_compress(response: Response, min_size: int = 1024) -> bool:
        """
        Determine if response should be compressed.
        
        Args:
            response: Flask response object
            min_size: Minimum response size to compress
            
        Returns:
            True if response should be compressed
        """
        # Check if client accepts gzip
        if 'gzip' not in request.headers.get('Accept-Encoding', ''):
            return False
        
        # Check response size
        if hasattr(response, 'content_length') and response.content_length:
            if response.content_length < min_size:
                return False
        
        # Check content type
        content_type = response.headers.get('Content-Type', '')
        compressible_types = [
            'application/json',
            'text/html',
            'text/plain',
            'text/css',
            'application/javascript'
        ]
        
        return any(ct in content_type for ct in compressible_types)

    @staticmethod
    def compress_response(response: Response) -> Response:
        """
        Compress response using gzip.
        
        Args:
            response: Flask response object
            
        Returns:
            Compressed response
        """
        try:
            if ResponseCompressor.should_compress(response):
                # Get response data
                data = response.get_data()
                
                # Compress data
                compressed_data = gzip.compress(data)
                
                # Update response
                response.set_data(compressed_data)
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = len(compressed_data)
                
                logger.debug(f"Compressed response: {len(data)} -> {len(compressed_data)} bytes")
            
            return response
            
        except Exception as e:
            logger.error(f"Error compressing response: {e}")
            return response


def compress_response(f: Callable) -> Callable:
    """
    Decorator to automatically compress API responses.
    
    Args:
        f: Function to decorate
        
    Returns:
        Decorated function
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)
        
        # Convert to Response object if needed
        if not isinstance(response, Response):
            response = jsonify(response)
        
        return ResponseCompressor.compress_response(response)
    
    return decorated_function


class APICacheManager:
    """
    API-specific caching manager.
    
    Provides intelligent caching for API endpoints with
    automatic cache invalidation and TTL management.
    """

    def __init__(self):
        """Initialize API cache manager."""
        self.cache = get_caching_manager()
        self.default_ttl = 60  # 1 minute default

    def generate_cache_key(self, endpoint: str, **params) -> str:
        """
        Generate cache key for API endpoint.
        
        Args:
            endpoint: API endpoint name
            **params: Request parameters
            
        Returns:
            Cache key string
        """
        # Sort parameters for consistent keys
        sorted_params = sorted(params.items())
        param_str = "&".join(f"{k}={v}" for k, v in sorted_params)
        return f"api:{endpoint}:{param_str}"

    def cache_api_response(self, endpoint: str, data: Any, ttl: Optional[int] = None, **params) -> bool:
        """
        Cache API response data.
        
        Args:
            endpoint: API endpoint name
            data: Response data to cache
            ttl: Time to live in seconds
            **params: Request parameters
            
        Returns:
            True if cached successfully
        """
        cache_key = self.generate_cache_key(endpoint, **params)
        return self.cache.set(cache_key, data, ttl or self.default_ttl)

    def get_cached_response(self, endpoint: str, **params) -> Optional[Any]:
        """
        Get cached API response.
        
        Args:
            endpoint: API endpoint name
            **params: Request parameters
            
        Returns:
            Cached response data or None
        """
        cache_key = self.generate_cache_key(endpoint, **params)
        return self.cache.get(cache_key)

    def invalidate_endpoint_cache(self, endpoint: str) -> bool:
        """
        Invalidate all cached responses for an endpoint.
        
        Args:
            endpoint: API endpoint name
            
        Returns:
            True if invalidation successful
        """
        # This is a simplified implementation
        # In production, you might want to use Redis pattern matching
        cache_pattern = f"api:{endpoint}:*"
        logger.info(f"Invalidating cache pattern: {cache_pattern}")
        return True


def cache_api_response(endpoint: str, ttl: int = 60):
    """
    Decorator to cache API responses.
    
    Args:
        endpoint: API endpoint name
        ttl: Cache time to live in seconds
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            cache_manager = APICacheManager()
            
            # Generate cache key from request parameters
            params = dict(request.args)
            if request.json:
                params.update(request.json)
            
            # Try to get cached response
            cached_response = cache_manager.get_cached_response(endpoint, **params)
            if cached_response is not None:
                logger.debug(f"Cache hit for endpoint {endpoint}")
                return jsonify(cached_response)
            
            # Execute function and cache result
            with operation_timer(logger, f"api_call_{endpoint}"):
                result = f(*args, **kwargs)
                
                # Cache the result
                if isinstance(result, (dict, list)):
                    cache_manager.cache_api_response(endpoint, result, ttl, **params)
                
                return result
        
        return decorated_function
    return decorator


class PaginationHelper:
    """
    Helper class for API pagination.
    
    Provides consistent pagination across API endpoints
    with performance optimizations.
    """

    @staticmethod
    def get_pagination_params() -> Tuple[int, int]:
        """
        Get pagination parameters from request.
        
        Returns:
            Tuple of (limit, offset)
        """
        try:
            limit = int(request.args.get('limit', 50))
            page = int(request.args.get('page', 1))
            
            # Validate limits
            limit = max(1, min(limit, 1000))  # Between 1 and 1000
            page = max(1, page)
            
            offset = (page - 1) * limit
            
            return limit, offset
            
        except (ValueError, TypeError):
            return 50, 0  # Default values

    @staticmethod
    def create_paginated_response(data: List[Any], total_count: int, 
                                limit: int, offset: int) -> Dict[str, Any]:
        """
        Create paginated response structure.
        
        Args:
            data: List of data items
            total_count: Total number of items available
            limit: Items per page
            offset: Current offset
            
        Returns:
            Paginated response dictionary
        """
        current_page = (offset // limit) + 1
        total_pages = (total_count + limit - 1) // limit  # Ceiling division
        
        return {
            "data": data,
            "pagination": {
                "current_page": current_page,
                "total_pages": total_pages,
                "total_items": total_count,
                "items_per_page": limit,
                "has_next": current_page < total_pages,
                "has_prev": current_page > 1
            }
        }


def paginated_response(f: Callable) -> Callable:
    """
    Decorator to add pagination to API responses.
    
    Args:
        f: Function to decorate
        
    Returns:
        Decorated function with pagination
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        limit, offset = PaginationHelper.get_pagination_params()
        
        # Add pagination parameters to function kwargs
        kwargs['limit'] = limit
        kwargs['offset'] = offset
        
        result = f(*args, **kwargs)
        
        # If result is a tuple (data, total_count), create paginated response
        if isinstance(result, tuple) and len(result) == 2:
            data, total_count = result
            return PaginationHelper.create_paginated_response(data, total_count, limit, offset)
        
        return result
    
    return decorated_function


class AsyncRequestHandler:
    """
    Async request handler for long-running operations.
    
    Provides async processing capabilities for API endpoints
    that perform time-consuming operations.
    """

    def __init__(self, max_workers: int = 10):
        """Initialize async request handler."""
        self.executor = ThreadPoolExecutor(max_workers=max_workers)

    async def execute_async(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function asynchronously.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, func, *args, **kwargs)

    def shutdown(self):
        """Shutdown the async handler."""
        self.executor.shutdown(wait=True)


# Global async handler
_async_handler: Optional[AsyncRequestHandler] = None


def get_async_handler() -> AsyncRequestHandler:
    """Get the global async request handler."""
    global _async_handler
    if _async_handler is None:
        _async_handler = AsyncRequestHandler()
    return _async_handler


def async_endpoint(f: Callable) -> Callable:
    """
    Decorator to make API endpoints async.
    
    Args:
        f: Function to decorate
        
    Returns:
        Decorated async function
    """
    @wraps(f)
    async def decorated_function(*args, **kwargs):
        handler = get_async_handler()
        return await handler.execute_async(f, *args, **kwargs)
    
    return decorated_function


class PerformanceMonitor:
    """
    API performance monitoring.
    
    Tracks API endpoint performance metrics including
    response times, cache hit rates, and error rates.
    """

    def __init__(self):
        """Initialize performance monitor."""
        self.metrics: Dict[str, List[Dict[str, Any]]] = {}
        self.max_metrics_per_endpoint = 1000

    def record_request(self, endpoint: str, duration: float, 
                      status_code: int, cache_hit: bool = False) -> None:
        """
        Record API request metrics.
        
        Args:
            endpoint: API endpoint name
            duration: Request duration in seconds
            status_code: HTTP status code
            cache_hit: Whether request was served from cache
        """
        if endpoint not in self.metrics:
            self.metrics[endpoint] = []

        metric = {
            "timestamp": time.time(),
            "duration": duration,
            "status_code": status_code,
            "cache_hit": cache_hit
        }

        self.metrics[endpoint].append(metric)

        # Keep only recent metrics
        if len(self.metrics[endpoint]) > self.max_metrics_per_endpoint:
            self.metrics[endpoint] = self.metrics[endpoint][-self.max_metrics_per_endpoint:]

    def get_endpoint_stats(self, endpoint: str) -> Dict[str, Any]:
        """
        Get performance statistics for an endpoint.
        
        Args:
            endpoint: API endpoint name
            
        Returns:
            Performance statistics dictionary
        """
        if endpoint not in self.metrics or not self.metrics[endpoint]:
            return {"endpoint": endpoint, "total_requests": 0}

        metrics = self.metrics[endpoint]
        durations = [m["duration"] for m in metrics]
        cache_hits = sum(1 for m in metrics if m["cache_hit"])
        errors = sum(1 for m in metrics if m["status_code"] >= 400)

        return {
            "endpoint": endpoint,
            "total_requests": len(metrics),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "cache_hit_rate": cache_hits / len(metrics),
            "error_rate": errors / len(metrics),
            "requests_per_minute": self._calculate_rpm(metrics)
        }

    def _calculate_rpm(self, metrics: List[Dict[str, Any]]) -> float:
        """Calculate requests per minute."""
        if len(metrics) < 2:
            return 0.0

        time_span = metrics[-1]["timestamp"] - metrics[0]["timestamp"]
        if time_span == 0:
            return 0.0

        return (len(metrics) / time_span) * 60

    def get_overall_stats(self) -> Dict[str, Any]:
        """Get overall API performance statistics."""
        total_requests = sum(len(metrics) for metrics in self.metrics.values())
        
        if total_requests == 0:
            return {"total_requests": 0}

        all_durations = []
        total_cache_hits = 0
        total_errors = 0

        for metrics in self.metrics.values():
            all_durations.extend(m["duration"] for m in metrics)
            total_cache_hits += sum(1 for m in metrics if m["cache_hit"])
            total_errors += sum(1 for m in metrics if m["status_code"] >= 400)

        return {
            "total_requests": total_requests,
            "avg_duration": sum(all_durations) / len(all_durations),
            "overall_cache_hit_rate": total_cache_hits / total_requests,
            "overall_error_rate": total_errors / total_requests,
            "endpoints": list(self.metrics.keys())
        }


# Global performance monitor
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """Get the global performance monitor."""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def monitor_performance(endpoint: str):
    """
    Decorator to monitor API endpoint performance.
    
    Args:
        endpoint: API endpoint name
        
    Returns:
        Decorator function
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            monitor = get_performance_monitor()
            start_time = time.time()
            
            try:
                with request_context(component="API", operation=endpoint):
                    result = f(*args, **kwargs)
                    
                    duration = time.time() - start_time
                    status_code = getattr(result, 'status_code', 200)
                    
                    monitor.record_request(endpoint, duration, status_code)
                    
                    return result
                    
            except Exception as e:
                duration = time.time() - start_time
                monitor.record_request(endpoint, duration, 500)
                raise
        
        return decorated_function
    return decorator
